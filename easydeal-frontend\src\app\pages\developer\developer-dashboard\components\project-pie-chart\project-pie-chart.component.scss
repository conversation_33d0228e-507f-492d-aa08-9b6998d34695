// Project Pie Chart component styles
.card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
}

.card-title {
  margin-bottom: 0;
}

.card-label {
  color: var(--bs-gray-900);
  font-weight: 600;
}

.card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-body {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

// Chart container
#kt_card_widget_17_chart {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  border-radius: 50%;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

// Legend styles
.legend-container {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.bullet {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.legend-text {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin: 0;
}

// Color classes
.bg-warning {
  background-color: #ffc107 !important;
}

.bg-success {
  background-color: #28a745 !important;
}

.bg-primary {
  background-color: #007bff !important;
}

.bg-danger {
  background-color: #dc3545 !important;
}

// Responsive design
@media (max-width: 768px) {
  .card-body {
    padding: 20px 15px;
  }

  #kt_card_widget_17_chart {
    width: 120px !important;
    height: 120px !important;
  }

  .legend-container {
    margin-top: 15px;
    padding: 10px;
  }

  .legend-item {
    padding: 6px 10px;
    margin-bottom: 6px;
  }

  .legend-text {
    font-size: 13px;
  }

  .bullet {
    width: 10px;
    height: 10px;
    margin-right: 8px;
  }
}
