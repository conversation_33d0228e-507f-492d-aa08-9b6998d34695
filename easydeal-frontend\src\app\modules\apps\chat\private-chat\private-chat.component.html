<div
  class="flex-column flex-lg-row-auto w-100 w-lg-300px w-xl-400px mb-10 mb-lg-0"
>
  <div class="card card-flush">
    <div class="card-header pt-7" id="kt_chat_contacts_header">
      <form class="w-100 position-relative" autoComplete="off">
        <app-keenicon
          name="magnifier"
          class="fs-2 position-absolute top-50 ms-5 translate-middle-y"
          type="outline"
        ></app-keenicon>
        <input
          type="text"
          class="form-control form-control-solid px-15"
          name="search"
          placeholder="Search by username or email..."
        />
      </form>
    </div>

    <div class="card-body pt-5" id="kt_chat_contacts_body">
      <div
        class="scroll-y me-n5 pe-5 h-200px h-lg-auto"
        data-kt-scroll="true"
        data-kt-scroll-activate="{default: false, lg: true}"
        data-kt-scroll-max-height="auto"
        data-kt-scroll-dependencies="#kt_header, #kt_toolbar, #kt_footer, #kt_chat_contacts_header"
        data-kt-scroll-wrappers="#kt_content, #kt_chat_contacts_body"
        data-kt-scroll-offset="0px"
      >
        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <span
                class="symbol-label bg-light-danger text-danger fs-6 fw-bolder"
              >
                M
              </span>
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Melody Macy
              </a>
              <div class="fw-bold text-gray-500">melody&#64;altbox.com</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">5 hrs</span>
          </div>
        </div>

        <div class="separator separator-dashed d-none"></div>

        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <img alt="Pic" src="./assets/media/avatars/300-1.jpg" />
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Max Smith
              </a>
              <div class="fw-bold text-gray-500">max&#64;kt.com</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">20 hrs</span>
          </div>
        </div>

        <div class="separator separator-dashed d-none"></div>

        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <img alt="Pic" src="./assets/media/avatars/300-5.jpg" />
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Sean Bean
              </a>
              <div class="fw-bold text-gray-500">sean&#64;dellito.com</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">20 hrs</span>
            <span class="badge badge-sm badge-circle badge-light-success"
              >6</span
            >
          </div>
        </div>

        <div class="separator separator-dashed d-none"></div>

        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <img alt="Pic" src="./assets/media/avatars/300-25.jpg" />
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Brian Cox
              </a>
              <div class="fw-bold text-gray-500">brian&#64;exchange.com</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">20 hrs</span>
          </div>
        </div>

        <div class="separator separator-dashed d-none"></div>

        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <span
                class="symbol-label bg-light-warning text-warning fs-6 fw-bolder"
              >
                M
              </span>
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Mikaela Collins
              </a>
              <div class="fw-bold text-gray-500">mikaela&#64;pexcom.com</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">1 day</span>
          </div>
        </div>

        <div class="separator separator-dashed d-none"></div>

        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <img alt="Pic" src="./assets/media/avatars/300-9.jpg" />
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Francis Mitcham
              </a>
              <div class="fw-bold text-gray-500">f.mitcham&#64;kpmg.com.au</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">5 hrs</span>
            <span class="badge badge-sm badge-circle badge-light-success"
              >6</span
            >
          </div>
        </div>

        <div class="separator separator-dashed d-none"></div>

        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <span
                class="symbol-label bg-light-danger text-danger fs-6 fw-bolder"
              >
                O
              </span>
              <div
                class="symbol-badge bg-success start-100 top-100 border-4 h-15px w-15px ms-n2 mt-n2"
              ></div>
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Olivia Wild
              </a>
              <div class="fw-bold text-gray-500">olivia&#64;corpmail.com</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">1 week</span>
          </div>
        </div>

        <div class="separator separator-dashed d-none"></div>

        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <span
                class="symbol-label bg-light-primary text-primary fs-6 fw-bolder"
              >
                N
              </span>
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Neil Owen
              </a>
              <div class="fw-bold text-gray-500">owen.neil&#64;gmail.com</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">20 hrs</span>
            <span class="badge badge-sm badge-circle badge-light-success"
              >6</span
            >
          </div>
        </div>

        <div class="separator separator-dashed d-none"></div>

        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <img alt="Pic" src="./assets/media/avatars/300-23.jpg" />
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Dan Wilson
              </a>
              <div class="fw-bold text-gray-500">dam&#64;consilting.com</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">2 weeks</span>
            <span class="badge badge-sm badge-circle badge-light-warning"
              >9</span
            >
          </div>
        </div>

        <div class="separator separator-dashed d-none"></div>

        <div class="d-flex flex-stack py-4">
          <div class="d-flex align-items-center">
            <div class="symbol symbol-45px symbol-circle">
              <span
                class="symbol-label bg-light-danger text-danger fs-6 fw-bolder"
              >
                E
              </span>
              <div
                class="symbol-badge bg-success start-100 top-100 border-4 h-15px w-15px ms-n2 mt-n2"
              ></div>
            </div>

            <div class="ms-5">
              <a
                class="fs-5 fw-bolder text-gray-900 text-hover-primary mb-2 cursor-pointer"
              >
                Emma Bold
              </a>
              <div class="fw-bold text-gray-500">emma&#64;intenso.com</div>
            </div>
          </div>

          <div class="d-flex flex-column align-items-end ms-2">
            <span class="text-muted fs-7 mb-1">1 day</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="flex-lg-row-fluid ms-lg-7 ms-xl-10">
  <div class="card" id="kt_chat_messenger">
    <div class="card-header" id="kt_chat_messenger_header">
      <div class="card-title">
        <div class="symbol-group symbol-hover"></div>
        <div class="d-flex justify-content-center flex-column me-3">
          <a
            class="fs-4 fw-bolder text-gray-900 text-hover-primary me-1 mb-2 lh-1 cursor-pointer"
          >
            Brian Cox
          </a>

          <div class="mb-0 lh-1">
            <span
              class="badge badge-success badge-circle w-10px h-10px me-1"
            ></span>
            <span class="fs-7 fw-bold text-gray-500">Active</span>
          </div>
        </div>
      </div>

      <div class="card-toolbar">
        <div class="me-n3">
          <button
            class="btn btn-sm btn-icon btn-active-light-primary"
            data-kt-menu-trigger="click"
            data-kt-menu-placement="bottom-end"
            data-kt-menu-flip="top-end"
          >
            <i class="bi bi-three-dots fs-2"></i>
          </button>
          <app-dropdown-menu1></app-dropdown-menu1>
        </div>
      </div>
    </div>
    <app-chat-inner></app-chat-inner>
  </div>
</div>
