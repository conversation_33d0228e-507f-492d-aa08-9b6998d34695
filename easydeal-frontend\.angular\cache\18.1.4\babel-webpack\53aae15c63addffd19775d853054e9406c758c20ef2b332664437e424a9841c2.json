{"ast": null, "code": "import baseClone from './_baseClone.js';\nimport baseMatches from './_baseMatches.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1;\n\n/**\n * Creates a function that performs a partial deep comparison between a given\n * object and `source`, returning `true` if the given object has equivalent\n * property values, else `false`.\n *\n * **Note:** The created function is equivalent to `_.isMatch` with `source`\n * partially applied.\n *\n * Partial comparisons will match empty array and empty object `source`\n * values against any array or object value, respectively. See `_.isEqual`\n * for a list of supported value comparisons.\n *\n * **Note:** Multiple values can be checked by combining several matchers\n * using `_.overSome`\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Util\n * @param {Object} source The object of property values to match.\n * @returns {Function} Returns the new spec function.\n * @example\n *\n * var objects = [\n *   { 'a': 1, 'b': 2, 'c': 3 },\n *   { 'a': 4, 'b': 5, 'c': 6 }\n * ];\n *\n * _.filter(objects, _.matches({ 'a': 4, 'c': 6 }));\n * // => [{ 'a': 4, 'b': 5, 'c': 6 }]\n *\n * // Checking for several possible values\n * _.filter(objects, _.overSome([_.matches({ 'a': 1 }), _.matches({ 'a': 4 })]));\n * // => [{ 'a': 1, 'b': 2, 'c': 3 }, { 'a': 4, 'b': 5, 'c': 6 }]\n */\nfunction matches(source) {\n  return baseMatches(baseClone(source, CLONE_DEEP_FLAG));\n}\nexport default matches;", "map": {"version": 3, "names": ["baseClone", "baseMatches", "CLONE_DEEP_FLAG", "matches", "source"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/matches.js"], "sourcesContent": ["import baseClone from './_baseClone.js';\nimport baseMatches from './_baseMatches.js';\n\n/** Used to compose bitmasks for cloning. */\nvar CLONE_DEEP_FLAG = 1;\n\n/**\n * Creates a function that performs a partial deep comparison between a given\n * object and `source`, returning `true` if the given object has equivalent\n * property values, else `false`.\n *\n * **Note:** The created function is equivalent to `_.isMatch` with `source`\n * partially applied.\n *\n * Partial comparisons will match empty array and empty object `source`\n * values against any array or object value, respectively. See `_.isEqual`\n * for a list of supported value comparisons.\n *\n * **Note:** Multiple values can be checked by combining several matchers\n * using `_.overSome`\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Util\n * @param {Object} source The object of property values to match.\n * @returns {Function} Returns the new spec function.\n * @example\n *\n * var objects = [\n *   { 'a': 1, 'b': 2, 'c': 3 },\n *   { 'a': 4, 'b': 5, 'c': 6 }\n * ];\n *\n * _.filter(objects, _.matches({ 'a': 4, 'c': 6 }));\n * // => [{ 'a': 4, 'b': 5, 'c': 6 }]\n *\n * // Checking for several possible values\n * _.filter(objects, _.overSome([_.matches({ 'a': 1 }), _.matches({ 'a': 4 })]));\n * // => [{ 'a': 1, 'b': 2, 'c': 3 }, { 'a': 4, 'b': 5, 'c': 6 }]\n */\nfunction matches(source) {\n  return baseMatches(baseClone(source, CLONE_DEEP_FLAG));\n}\n\nexport default matches;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA,IAAIC,eAAe,GAAG,CAAC;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAE;EACvB,OAAOH,WAAW,CAACD,SAAS,CAACI,MAAM,EAAEF,eAAe,CAAC,CAAC;AACxD;AAEA,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}