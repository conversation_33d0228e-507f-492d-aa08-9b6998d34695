{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseSortedUniq from './_baseSortedUniq.js';\n\n/**\n * This method is like `_.uniqBy` except that it's designed and optimized\n * for sorted arrays.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.sortedUniqBy([1.1, 1.2, 2.3, 2.4], Math.floor);\n * // => [1.1, 2.3]\n */\nfunction sortedUniqBy(array, iteratee) {\n  return array && array.length ? baseSortedUniq(array, baseIteratee(iteratee, 2)) : [];\n}\nexport default sortedUniqBy;", "map": {"version": 3, "names": ["baseIteratee", "baseSortedUniq", "sortedUniqBy", "array", "iteratee", "length"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/sortedUniqBy.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport baseSortedUniq from './_baseSortedUniq.js';\n\n/**\n * This method is like `_.uniqBy` except that it's designed and optimized\n * for sorted arrays.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.sortedUniqBy([1.1, 1.2, 2.3, 2.4], Math.floor);\n * // => [1.1, 2.3]\n */\nfunction sortedUniqBy(array, iteratee) {\n  return (array && array.length)\n    ? baseSortedUniq(array, baseIteratee(iteratee, 2))\n    : [];\n}\n\nexport default sortedUniqBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACrC,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GACzBJ,cAAc,CAACE,KAAK,EAAEH,YAAY,CAACI,QAAQ,EAAE,CAAC,CAAC,CAAC,GAChD,EAAE;AACR;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}