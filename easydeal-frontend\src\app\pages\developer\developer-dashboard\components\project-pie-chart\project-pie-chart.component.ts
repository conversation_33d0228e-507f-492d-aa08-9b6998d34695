import { Component, Input, OnInit, OnChanges } from '@angular/core';

@Component({
  selector: 'app-project-pie-chart',
  templateUrl: './project-pie-chart.component.html',
  styleUrls: ['./project-pie-chart.component.scss']
})
export class ProjectPieChartComponent implements OnInit, OnChanges {

  @Input() title: string = 'Sales';
  @Input() subtitle: string = 'Over 500 new projects';
  @Input() cssClass: string = '';
  @Input() chartSize: number = 150;
  @Input() chartLine: number = 30;
  @Input() chartRotate: number = 145;

  @Input() newCount: number = 0;
  @Input() availableCount: number = 0;
  @Input() soldCount: number = 0;
  @Input() reservedCount: number = 0;

  constructor() {}

  ngOnInit(): void {
    setTimeout(() => {
      this.initChart();
    }, 10);
  }

  ngOnChanges(): void {
    setTimeout(() => {
      this.initChart();
    }, 10);
  }

  get totalCount(): number {
    return this.newCount + this.availableCount + this.soldCount + this.reservedCount;
  }

  get newPercent(): number {
    return this.totalCount === 0 ? 0 : Math.round((this.newCount / this.totalCount) * 100);
  }

  get availablePercent(): number {
    return this.totalCount === 0 ? 0 : Math.round((this.availableCount / this.totalCount) * 100);
  }

  get soldPercent(): number {
    return this.totalCount === 0 ? 0 : Math.round((this.soldCount / this.totalCount) * 100);
  }

  get reservedPercent(): number {
    return this.totalCount === 0 ? 0 : Math.round((this.reservedCount / this.totalCount) * 100);
  }

  private initChart() {
    console.log('Initializing chart with data:', {
      new: this.newCount,
      available: this.availableCount,
      sold: this.soldCount,
      reserved: this.reservedCount,
      total: this.totalCount,
      percentages: {
        new: this.newPercent,
        available: this.availablePercent,
        sold: this.soldPercent,
        reserved: this.reservedPercent
      }
    });

    const el = document.getElementById('kt_card_widget_17_chart');
    if (!el) {
      console.error('Chart element not found');
      return;
    }

     if (this.totalCount === 0) {
      el.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 150px; color: #999;">No data available</div>';
      return;
    }

    const options = {
      size: this.chartSize,
      lineWidth: this.chartLine,
      rotate: this.chartRotate
    };

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = canvas.height = options.size;

    el.innerHTML = '';
    el.appendChild(canvas);

    if (!ctx) return;

     ctx.translate(options.size / 2, options.size / 2);
    ctx.rotate((-1 / 2 + options.rotate / 180) * Math.PI);

    const radius = (options.size - options.lineWidth) / 2;

     const drawCircle = (color: string, lineWidth: number, startPercent: number, endPercent: number) => {
      ctx.beginPath();
      ctx.arc(
        0,
        0,
        radius,
        Math.PI * 2 * (startPercent / 100),
        Math.PI * 2 * (endPercent / 100),
        false
      );
      ctx.strokeStyle = color;
      ctx.lineCap = 'round';
      ctx.lineWidth = lineWidth;
      ctx.stroke();
    };

    let startPercent = 0;

     if (this.newCount > 0) {
      drawCircle('#ffc107', options.lineWidth, startPercent, startPercent + this.newPercent);
      startPercent += this.newPercent;
    }

     if (this.availableCount > 0) {
      drawCircle('#28a745', options.lineWidth, startPercent, startPercent + this.availablePercent);
      startPercent += this.availablePercent;
    }

     if (this.soldCount > 0) {
      drawCircle('#007bff', options.lineWidth, startPercent, startPercent + this.soldPercent);
      startPercent += this.soldPercent;
    }

     if (this.reservedCount > 0) {
      drawCircle('#dc3545', options.lineWidth, startPercent, startPercent + this.reservedPercent);
    }
  }
}
