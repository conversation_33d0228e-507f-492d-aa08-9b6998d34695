 .card {
  height: 70%;
   width: 90%;

  &.bg-primary,
  &.bg-success,
  &.bg-danger,
  &.bg-warning {
    .card-header {
      border-bottom: none;
    }
  }
}

.card-header {
  padding: 10px 15px;

  .badge {
    font-size: 0.85rem;
    padding: 0.65rem;
    border-radius: 6px;
    height: 30px;
  }
}

.card-body {
  padding: 0;
}

.card-p {
  padding: 20px 15px;
  min-height: 80px;
}

.stat-item {
  flex: 1;
}

.stat-label {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  opacity: 0.9;

}

.stat-value {
  font-size: 1rem;
  font-weight: 700;
  line-height: 1;
}



 @media (max-width: 1200px) {
  .card-header {
    padding: 8px 12px;
  }

  .card-p {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .card-header {
    padding: 8px 10px;
  }

  .card-p {
    padding: 10px;
  }

  .stat-value {
    font-size: 1.4rem !important;
  }

  .stat-label {
    font-size: 0.8rem !important;
  }
}
