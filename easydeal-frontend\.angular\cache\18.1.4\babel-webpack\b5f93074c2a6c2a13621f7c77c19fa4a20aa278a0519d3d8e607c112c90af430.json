{"ast": null, "code": "import castPath from './_castPath.js';\nimport isFunction from './isFunction.js';\nimport toKey from './_toKey.js';\n\n/**\n * This method is like `_.get` except that if the resolved value is a\n * function it's invoked with the `this` binding of its parent object and\n * its result is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to resolve.\n * @param {*} [defaultValue] The value returned for `undefined` resolved values.\n * @returns {*} Returns the resolved value.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c1': 3, 'c2': _.constant(4) } }] };\n *\n * _.result(object, 'a[0].b.c1');\n * // => 3\n *\n * _.result(object, 'a[0].b.c2');\n * // => 4\n *\n * _.result(object, 'a[0].b.c3', 'default');\n * // => 'default'\n *\n * _.result(object, 'a[0].b.c3', _.constant('default'));\n * // => 'default'\n */\nfunction result(object, path, defaultValue) {\n  path = castPath(path, object);\n  var index = -1,\n    length = path.length;\n\n  // Ensure the loop is entered when path is empty.\n  if (!length) {\n    length = 1;\n    object = undefined;\n  }\n  while (++index < length) {\n    var value = object == null ? undefined : object[toKey(path[index])];\n    if (value === undefined) {\n      index = length;\n      value = defaultValue;\n    }\n    object = isFunction(value) ? value.call(object) : value;\n  }\n  return object;\n}\nexport default result;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "isFunction", "to<PERSON><PERSON>", "result", "object", "path", "defaultValue", "index", "length", "undefined", "value", "call"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/result.js"], "sourcesContent": ["import castPath from './_castPath.js';\nimport isFunction from './isFunction.js';\nimport toKey from './_toKey.js';\n\n/**\n * This method is like `_.get` except that if the resolved value is a\n * function it's invoked with the `this` binding of its parent object and\n * its result is returned.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to resolve.\n * @param {*} [defaultValue] The value returned for `undefined` resolved values.\n * @returns {*} Returns the resolved value.\n * @example\n *\n * var object = { 'a': [{ 'b': { 'c1': 3, 'c2': _.constant(4) } }] };\n *\n * _.result(object, 'a[0].b.c1');\n * // => 3\n *\n * _.result(object, 'a[0].b.c2');\n * // => 4\n *\n * _.result(object, 'a[0].b.c3', 'default');\n * // => 'default'\n *\n * _.result(object, 'a[0].b.c3', _.constant('default'));\n * // => 'default'\n */\nfunction result(object, path, defaultValue) {\n  path = castPath(path, object);\n\n  var index = -1,\n      length = path.length;\n\n  // Ensure the loop is entered when path is empty.\n  if (!length) {\n    length = 1;\n    object = undefined;\n  }\n  while (++index < length) {\n    var value = object == null ? undefined : object[toKey(path[index])];\n    if (value === undefined) {\n      index = length;\n      value = defaultValue;\n    }\n    object = isFunction(value) ? value.call(object) : value;\n  }\n  return object;\n}\n\nexport default result;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,IAAI,EAAEC,YAAY,EAAE;EAC1CD,IAAI,GAAGL,QAAQ,CAACK,IAAI,EAAED,MAAM,CAAC;EAE7B,IAAIG,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,IAAI,CAACG,MAAM;;EAExB;EACA,IAAI,CAACA,MAAM,EAAE;IACXA,MAAM,GAAG,CAAC;IACVJ,MAAM,GAAGK,SAAS;EACpB;EACA,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,KAAK,GAAGN,MAAM,IAAI,IAAI,GAAGK,SAAS,GAAGL,MAAM,CAACF,KAAK,CAACG,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC;IACnE,IAAIG,KAAK,KAAKD,SAAS,EAAE;MACvBF,KAAK,GAAGC,MAAM;MACdE,KAAK,GAAGJ,YAAY;IACtB;IACAF,MAAM,GAAGH,UAAU,CAACS,KAAK,CAAC,GAAGA,KAAK,CAACC,IAAI,CAACP,MAAM,CAAC,GAAGM,KAAK;EACzD;EACA,OAAON,MAAM;AACf;AAEA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}