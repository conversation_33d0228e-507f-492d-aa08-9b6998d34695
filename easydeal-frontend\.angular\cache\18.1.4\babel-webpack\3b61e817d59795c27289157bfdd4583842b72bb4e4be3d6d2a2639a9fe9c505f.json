{"ast": null, "code": "import baseFunctions from './_baseFunctions.js';\nimport keys from './keys.js';\n\n/**\n * Creates an array of function property names from own enumerable properties\n * of `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to inspect.\n * @returns {Array} Returns the function names.\n * @see _.functionsIn\n * @example\n *\n * function Foo() {\n *   this.a = _.constant('a');\n *   this.b = _.constant('b');\n * }\n *\n * Foo.prototype.c = _.constant('c');\n *\n * _.functions(new Foo);\n * // => ['a', 'b']\n */\nfunction functions(object) {\n  return object == null ? [] : baseFunctions(object, keys(object));\n}\nexport default functions;", "map": {"version": 3, "names": ["baseFunctions", "keys", "functions", "object"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/functions.js"], "sourcesContent": ["import baseFunctions from './_baseFunctions.js';\nimport keys from './keys.js';\n\n/**\n * Creates an array of function property names from own enumerable properties\n * of `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The object to inspect.\n * @returns {Array} Returns the function names.\n * @see _.functionsIn\n * @example\n *\n * function Foo() {\n *   this.a = _.constant('a');\n *   this.b = _.constant('b');\n * }\n *\n * Foo.prototype.c = _.constant('c');\n *\n * _.functions(new Foo);\n * // => ['a', 'b']\n */\nfunction functions(object) {\n  return object == null ? [] : baseFunctions(object, keys(object));\n}\n\nexport default functions;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,MAAM,EAAE;EACzB,OAAOA,MAAM,IAAI,IAAI,GAAG,EAAE,GAAGH,aAAa,CAACG,MAAM,EAAEF,IAAI,CAACE,MAAM,CAAC,CAAC;AAClE;AAEA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}