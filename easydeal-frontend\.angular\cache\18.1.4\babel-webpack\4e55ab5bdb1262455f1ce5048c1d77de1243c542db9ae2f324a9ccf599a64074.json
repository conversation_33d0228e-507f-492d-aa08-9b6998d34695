{"ast": null, "code": "import getWrapDetails from './_getWrapDetails.js';\nimport insertWrapDetails from './_insertWrapDetails.js';\nimport setToString from './_setToString.js';\nimport updateWrapDetails from './_updateWrapDetails.js';\n\n/**\n * Sets the `toString` method of `wrapper` to mimic the source of `reference`\n * with wrapper details in a comment at the top of the source body.\n *\n * @private\n * @param {Function} wrapper The function to modify.\n * @param {Function} reference The reference function.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @returns {Function} Returns `wrapper`.\n */\nfunction setWrapToString(wrapper, reference, bitmask) {\n  var source = reference + '';\n  return setToString(wrapper, insertWrapDetails(source, updateWrapDetails(getWrapDetails(source), bitmask)));\n}\nexport default setWrapToString;", "map": {"version": 3, "names": ["getWrapDetails", "insertWrapDetails", "setToString", "updateWrapDetails", "setWrapToString", "wrapper", "reference", "bitmask", "source"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_setWrapToString.js"], "sourcesContent": ["import getWrapDetails from './_getWrapDetails.js';\nimport insertWrapDetails from './_insertWrapDetails.js';\nimport setToString from './_setToString.js';\nimport updateWrapDetails from './_updateWrapDetails.js';\n\n/**\n * Sets the `toString` method of `wrapper` to mimic the source of `reference`\n * with wrapper details in a comment at the top of the source body.\n *\n * @private\n * @param {Function} wrapper The function to modify.\n * @param {Function} reference The reference function.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @returns {Function} Returns `wrapper`.\n */\nfunction setWrapToString(wrapper, reference, bitmask) {\n  var source = (reference + '');\n  return setToString(wrapper, insertWrapDetails(source, updateWrapDetails(getWrapDetails(source), bitmask)));\n}\n\nexport default setWrapToString;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,iBAAiB,MAAM,yBAAyB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;EACpD,IAAIC,MAAM,GAAIF,SAAS,GAAG,EAAG;EAC7B,OAAOJ,WAAW,CAACG,OAAO,EAAEJ,iBAAiB,CAACO,MAAM,EAAEL,iBAAiB,CAACH,cAAc,CAACQ,MAAM,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC;AAC5G;AAEA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}