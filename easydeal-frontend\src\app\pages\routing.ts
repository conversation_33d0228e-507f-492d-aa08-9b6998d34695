import { Routes } from '@angular/router';

const Routing: Routes = [


  {
    path: 'client',
    loadChildren: () =>
      import('./user/client/client.module').then((m) => m.ClientModule),
  },

  {
    path: 'profile',
    loadChildren: () =>
      import('./shared/profile/profile-routing.module').then(
        (m) => m.ProfileRoutingModule
      ),
  },

  {
    path: 'broker',
    loadChildren: () =>
      import('./broker/broker.module').then((m) => m.BrokerModule),
  },
  {
    path: 'requests',
    loadChildren: () =>
      import('./requests/requests.module').then((m) => m.RequestsModule),
  },
  {
    path: 'developer',
    loadChildren: () =>
      import('./developer/developer.module').then((m) => m.DeveloperModule),
  },
  {
    path: 'builder',
    loadChildren: () =>
      import('./builder/builder.module').then((m) => m.BuilderModule),
  },
  {
    path: 'Signup',
    loadChildren: () =>
      import('./Auth/SignUp/signup.module').then((m) => m.SignupModule),
  },
  {
    path: 'crafted/pages/profile',
    loadChildren: () =>
      import('../modules/profile/profile.module').then((m) => m.ProfileModule),
    // data: { layout: 'light-sidebar' },
  },
  {
    path: 'crafted/account',
    loadChildren: () =>
      import('../modules/account/account.module').then((m) => m.AccountModule),
    // data: { layout: 'dark-header' },
  },
  {
    path: 'crafted/pages/wizards',
    loadChildren: () =>
      import('../modules/wizards/wizards.module').then((m) => m.WizardsModule),
    // data: { layout: 'light-header' },
  },
  {
    path: 'crafted/widgets',
    loadChildren: () =>
      import('../modules/widgets-examples/widgets-examples.module').then(
        (m) => m.WidgetsExamplesModule
      ),
    // data: { layout: 'light-header' },
  },
  {
    path: 'apps/chat',
    loadChildren: () =>
      import('../modules/apps/chat/chat.module').then((m) => m.ChatModule),
    // data: { layout: 'light-sidebar' },
  },
  {
    path: 'apps/users',
    loadChildren: () => import('./user/user.module').then((m) => m.UserModule),
  },
  {
    path: 'apps/roles',
    loadChildren: () => import('./role/role.module').then((m) => m.RoleModule),
  },
  {
    path: 'apps/permissions',
    loadChildren: () =>
      import('./permission/permission.module').then((m) => m.PermissionModule),
  },
  {
    path: '',
    redirectTo: '/broker/dashboard',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'error/404',
  },
];

export { Routing };
