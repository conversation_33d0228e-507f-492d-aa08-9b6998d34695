{"ast": null, "code": "/**\n * The inverse of `_.toPairs`; this method returns an object composed\n * from key-value `pairs`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} pairs The key-value pairs.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.fromPairs([['a', 1], ['b', 2]]);\n * // => { 'a': 1, 'b': 2 }\n */\nfunction fromPairs(pairs) {\n  var index = -1,\n    length = pairs == null ? 0 : pairs.length,\n    result = {};\n  while (++index < length) {\n    var pair = pairs[index];\n    result[pair[0]] = pair[1];\n  }\n  return result;\n}\nexport default fromPairs;", "map": {"version": 3, "names": ["fromPairs", "pairs", "index", "length", "result", "pair"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/fromPairs.js"], "sourcesContent": ["/**\n * The inverse of `_.toPairs`; this method returns an object composed\n * from key-value `pairs`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} pairs The key-value pairs.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.fromPairs([['a', 1], ['b', 2]]);\n * // => { 'a': 1, 'b': 2 }\n */\nfunction fromPairs(pairs) {\n  var index = -1,\n      length = pairs == null ? 0 : pairs.length,\n      result = {};\n\n  while (++index < length) {\n    var pair = pairs[index];\n    result[pair[0]] = pair[1];\n  }\n  return result;\n}\n\nexport default fromPairs;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,KAAK,EAAE;EACxB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;IACzCC,MAAM,GAAG,CAAC,CAAC;EAEf,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,IAAI,GAAGJ,KAAK,CAACC,KAAK,CAAC;IACvBE,MAAM,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC;EAC3B;EACA,OAAOD,MAAM;AACf;AAEA,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}