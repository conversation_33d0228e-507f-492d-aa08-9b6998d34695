{"ast": null, "code": "import baseEach from './_baseEach.js';\n\n/**\n * The base implementation of `_.every` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`\n */\nfunction baseEvery(collection, predicate) {\n  var result = true;\n  baseEach(collection, function (value, index, collection) {\n    result = !!predicate(value, index, collection);\n    return result;\n  });\n  return result;\n}\nexport default baseEvery;", "map": {"version": 3, "names": ["baseEach", "baseEvery", "collection", "predicate", "result", "value", "index"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_baseEvery.js"], "sourcesContent": ["import baseEach from './_baseEach.js';\n\n/**\n * The base implementation of `_.every` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if all elements pass the predicate check,\n *  else `false`\n */\nfunction baseEvery(collection, predicate) {\n  var result = true;\n  baseEach(collection, function(value, index, collection) {\n    result = !!predicate(value, index, collection);\n    return result;\n  });\n  return result;\n}\n\nexport default baseEvery;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,UAAU,EAAEC,SAAS,EAAE;EACxC,IAAIC,MAAM,GAAG,IAAI;EACjBJ,QAAQ,CAACE,UAAU,EAAE,UAASG,KAAK,EAAEC,KAAK,EAAEJ,UAAU,EAAE;IACtDE,MAAM,GAAG,CAAC,CAACD,SAAS,CAACE,KAAK,EAAEC,KAAK,EAAEJ,UAAU,CAAC;IAC9C,OAAOE,MAAM;EACf,CAAC,CAAC;EACF,OAAOA,MAAM;AACf;AAEA,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}