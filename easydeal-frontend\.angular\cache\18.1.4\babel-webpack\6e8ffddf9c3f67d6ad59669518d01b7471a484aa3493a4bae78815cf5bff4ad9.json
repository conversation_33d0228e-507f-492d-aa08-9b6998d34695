{"ast": null, "code": "import arrayFilter from './_arrayFilter.js';\nimport baseRest from './_baseRest.js';\nimport baseXor from './_baseXor.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.xor` except that it accepts `comparator` which is\n * invoked to compare elements of `arrays`. The order of result values is\n * determined by the order they occur in the arrays. The comparator is invoked\n * with two arguments: (arrVal, othVal).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }];\n * var others = [{ 'x': 1, 'y': 1 }, { 'x': 1, 'y': 2 }];\n *\n * _.xorWith(objects, others, _.isEqual);\n * // => [{ 'x': 2, 'y': 1 }, { 'x': 1, 'y': 1 }]\n */\nvar xorWith = baseRest(function (arrays) {\n  var comparator = last(arrays);\n  comparator = typeof comparator == 'function' ? comparator : undefined;\n  return baseXor(arrayFilter(arrays, isArrayLikeObject), undefined, comparator);\n});\nexport default xorWith;", "map": {"version": 3, "names": ["arrayFilter", "baseRest", "baseXor", "isArrayLikeObject", "last", "xorWith", "arrays", "comparator", "undefined"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/xorWith.js"], "sourcesContent": ["import arrayFilter from './_arrayFilter.js';\nimport baseRest from './_baseRest.js';\nimport baseXor from './_baseXor.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.xor` except that it accepts `comparator` which is\n * invoked to compare elements of `arrays`. The order of result values is\n * determined by the order they occur in the arrays. The comparator is invoked\n * with two arguments: (arrVal, othVal).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }];\n * var others = [{ 'x': 1, 'y': 1 }, { 'x': 1, 'y': 2 }];\n *\n * _.xorWith(objects, others, _.isEqual);\n * // => [{ 'x': 2, 'y': 1 }, { 'x': 1, 'y': 1 }]\n */\nvar xorWith = baseRest(function(arrays) {\n  var comparator = last(arrays);\n  comparator = typeof comparator == 'function' ? comparator : undefined;\n  return baseXor(arrayFilter(arrays, isArrayLikeObject), undefined, comparator);\n});\n\nexport default xorWith;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGJ,QAAQ,CAAC,UAASK,MAAM,EAAE;EACtC,IAAIC,UAAU,GAAGH,IAAI,CAACE,MAAM,CAAC;EAC7BC,UAAU,GAAG,OAAOA,UAAU,IAAI,UAAU,GAAGA,UAAU,GAAGC,SAAS;EACrE,OAAON,OAAO,CAACF,WAAW,CAACM,MAAM,EAAEH,iBAAiB,CAAC,EAAEK,SAAS,EAAED,UAAU,CAAC;AAC/E,CAAC,CAAC;AAEF,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}