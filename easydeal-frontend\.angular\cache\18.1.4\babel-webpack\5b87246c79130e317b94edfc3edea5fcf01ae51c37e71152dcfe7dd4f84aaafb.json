{"ast": null, "code": "import toString from './toString.js';\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g,\n  reHasRegExpChar = RegExp(reRegExpChar.source);\n\n/**\n * Escapes the `RegExp` special characters \"^\", \"$\", \"\\\", \".\", \"*\", \"+\",\n * \"?\", \"(\", \")\", \"[\", \"]\", \"{\", \"}\", and \"|\" in `string`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to escape.\n * @returns {string} Returns the escaped string.\n * @example\n *\n * _.escapeRegExp('[lodash](https://lodash.com/)');\n * // => '\\[lodash\\]\\(https://lodash\\.com/\\)'\n */\nfunction escapeRegExp(string) {\n  string = toString(string);\n  return string && reHasRegExpChar.test(string) ? string.replace(reRegExpChar, '\\\\$&') : string;\n}\nexport default escapeRegExp;", "map": {"version": 3, "names": ["toString", "reRegExpChar", "reHasRegExpChar", "RegExp", "source", "escapeRegExp", "string", "test", "replace"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/escapeRegExp.js"], "sourcesContent": ["import toString from './toString.js';\n\n/**\n * Used to match `RegExp`\n * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).\n */\nvar reRegExpChar = /[\\\\^$.*+?()[\\]{}|]/g,\n    reHasRegExpChar = RegExp(reRegExpChar.source);\n\n/**\n * Escapes the `RegExp` special characters \"^\", \"$\", \"\\\", \".\", \"*\", \"+\",\n * \"?\", \"(\", \")\", \"[\", \"]\", \"{\", \"}\", and \"|\" in `string`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to escape.\n * @returns {string} Returns the escaped string.\n * @example\n *\n * _.escapeRegExp('[lodash](https://lodash.com/)');\n * // => '\\[lodash\\]\\(https://lodash\\.com/\\)'\n */\nfunction escapeRegExp(string) {\n  string = toString(string);\n  return (string && reHasRegExpChar.test(string))\n    ? string.replace(reRegExpChar, '\\\\$&')\n    : string;\n}\n\nexport default escapeRegExp;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,qBAAqB;EACpCC,eAAe,GAAGC,MAAM,CAACF,YAAY,CAACG,MAAM,CAAC;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,MAAM,EAAE;EAC5BA,MAAM,GAAGN,QAAQ,CAACM,MAAM,CAAC;EACzB,OAAQA,MAAM,IAAIJ,eAAe,CAACK,IAAI,CAACD,MAAM,CAAC,GAC1CA,MAAM,CAACE,OAAO,CAACP,YAAY,EAAE,MAAM,CAAC,GACpCK,MAAM;AACZ;AAEA,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}