{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport createInverter from './_createInverter.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * This method is like `_.invert` except that the inverted object is generated\n * from the results of running each element of `object` thru `iteratee`. The\n * corresponding inverted value of each inverted key is an array of keys\n * responsible for generating the inverted value. The iteratee is invoked\n * with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.1.0\n * @category Object\n * @param {Object} object The object to invert.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Object} Returns the new inverted object.\n * @example\n *\n * var object = { 'a': 1, 'b': 2, 'c': 1 };\n *\n * _.invertBy(object);\n * // => { '1': ['a', 'c'], '2': ['b'] }\n *\n * _.invertBy(object, function(value) {\n *   return 'group' + value;\n * });\n * // => { 'group1': ['a', 'c'], 'group2': ['b'] }\n */\nvar invertBy = createInverter(function (result, value, key) {\n  if (value != null && typeof value.toString != 'function') {\n    value = nativeObjectToString.call(value);\n  }\n  if (hasOwnProperty.call(result, value)) {\n    result[value].push(key);\n  } else {\n    result[value] = [key];\n  }\n}, baseIteratee);\nexport default invertBy;", "map": {"version": 3, "names": ["baseIteratee", "createInverter", "objectProto", "Object", "prototype", "hasOwnProperty", "nativeObjectToString", "toString", "invertBy", "result", "value", "key", "call", "push"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/invertBy.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport createInverter from './_createInverter.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * This method is like `_.invert` except that the inverted object is generated\n * from the results of running each element of `object` thru `iteratee`. The\n * corresponding inverted value of each inverted key is an array of keys\n * responsible for generating the inverted value. The iteratee is invoked\n * with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.1.0\n * @category Object\n * @param {Object} object The object to invert.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Object} Returns the new inverted object.\n * @example\n *\n * var object = { 'a': 1, 'b': 2, 'c': 1 };\n *\n * _.invertBy(object);\n * // => { '1': ['a', 'c'], '2': ['b'] }\n *\n * _.invertBy(object, function(value) {\n *   return 'group' + value;\n * });\n * // => { 'group1': ['a', 'c'], 'group2': ['b'] }\n */\nvar invertBy = createInverter(function(result, value, key) {\n  if (value != null &&\n      typeof value.toString != 'function') {\n    value = nativeObjectToString.call(value);\n  }\n\n  if (hasOwnProperty.call(result, value)) {\n    result[value].push(key);\n  } else {\n    result[value] = [key];\n  }\n}, baseIteratee);\n\nexport default invertBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAGJ,WAAW,CAACK,QAAQ;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAGP,cAAc,CAAC,UAASQ,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACzD,IAAID,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAACH,QAAQ,IAAI,UAAU,EAAE;IACvCG,KAAK,GAAGJ,oBAAoB,CAACM,IAAI,CAACF,KAAK,CAAC;EAC1C;EAEA,IAAIL,cAAc,CAACO,IAAI,CAACH,MAAM,EAAEC,KAAK,CAAC,EAAE;IACtCD,MAAM,CAACC,KAAK,CAAC,CAACG,IAAI,CAACF,GAAG,CAAC;EACzB,CAAC,MAAM;IACLF,MAAM,CAACC,KAAK,CAAC,GAAG,CAACC,GAAG,CAAC;EACvB;AACF,CAAC,EAAEX,YAAY,CAAC;AAEhB,eAAeQ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}