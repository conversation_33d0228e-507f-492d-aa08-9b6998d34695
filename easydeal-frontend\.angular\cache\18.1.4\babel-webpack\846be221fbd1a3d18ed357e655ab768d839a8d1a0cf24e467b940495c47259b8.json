{"ast": null, "code": "import apply from './_apply.js';\nimport createCtor from './_createCtor.js';\nimport createHybrid from './_createHybrid.js';\nimport createRecurry from './_createRecurry.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\nimport root from './_root.js';\n\n/**\n * Creates a function that wraps `func` to enable currying.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {number} arity The arity of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createCurry(func, bitmask, arity) {\n  var Ctor = createCtor(func);\n  function wrapper() {\n    var length = arguments.length,\n      args = Array(length),\n      index = length,\n      placeholder = getHolder(wrapper);\n    while (index--) {\n      args[index] = arguments[index];\n    }\n    var holders = length < 3 && args[0] !== placeholder && args[length - 1] !== placeholder ? [] : replaceHolders(args, placeholder);\n    length -= holders.length;\n    if (length < arity) {\n      return createRecurry(func, bitmask, createHybrid, wrapper.placeholder, undefined, args, holders, undefined, undefined, arity - length);\n    }\n    var fn = this && this !== root && this instanceof wrapper ? Ctor : func;\n    return apply(fn, this, args);\n  }\n  return wrapper;\n}\nexport default createCurry;", "map": {"version": 3, "names": ["apply", "createCtor", "createHybrid", "createRecurry", "getHolder", "replaceHolders", "root", "createCurry", "func", "bitmask", "arity", "Ctor", "wrapper", "length", "arguments", "args", "Array", "index", "placeholder", "holders", "undefined", "fn"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_createCurry.js"], "sourcesContent": ["import apply from './_apply.js';\nimport createCtor from './_createCtor.js';\nimport createHybrid from './_createHybrid.js';\nimport createRecurry from './_createRecurry.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\nimport root from './_root.js';\n\n/**\n * Creates a function that wraps `func` to enable currying.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {number} arity The arity of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createCurry(func, bitmask, arity) {\n  var Ctor = createCtor(func);\n\n  function wrapper() {\n    var length = arguments.length,\n        args = Array(length),\n        index = length,\n        placeholder = getHolder(wrapper);\n\n    while (index--) {\n      args[index] = arguments[index];\n    }\n    var holders = (length < 3 && args[0] !== placeholder && args[length - 1] !== placeholder)\n      ? []\n      : replaceHolders(args, placeholder);\n\n    length -= holders.length;\n    if (length < arity) {\n      return createRecurry(\n        func, bitmask, createHybrid, wrapper.placeholder, undefined,\n        args, holders, undefined, undefined, arity - length);\n    }\n    var fn = (this && this !== root && this instanceof wrapper) ? Ctor : func;\n    return apply(fn, this, args);\n  }\n  return wrapper;\n}\n\nexport default createCurry;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACzC,IAAIC,IAAI,GAAGV,UAAU,CAACO,IAAI,CAAC;EAE3B,SAASI,OAAOA,CAAA,EAAG;IACjB,IAAIC,MAAM,GAAGC,SAAS,CAACD,MAAM;MACzBE,IAAI,GAAGC,KAAK,CAACH,MAAM,CAAC;MACpBI,KAAK,GAAGJ,MAAM;MACdK,WAAW,GAAGd,SAAS,CAACQ,OAAO,CAAC;IAEpC,OAAOK,KAAK,EAAE,EAAE;MACdF,IAAI,CAACE,KAAK,CAAC,GAAGH,SAAS,CAACG,KAAK,CAAC;IAChC;IACA,IAAIE,OAAO,GAAIN,MAAM,GAAG,CAAC,IAAIE,IAAI,CAAC,CAAC,CAAC,KAAKG,WAAW,IAAIH,IAAI,CAACF,MAAM,GAAG,CAAC,CAAC,KAAKK,WAAW,GACpF,EAAE,GACFb,cAAc,CAACU,IAAI,EAAEG,WAAW,CAAC;IAErCL,MAAM,IAAIM,OAAO,CAACN,MAAM;IACxB,IAAIA,MAAM,GAAGH,KAAK,EAAE;MAClB,OAAOP,aAAa,CAClBK,IAAI,EAAEC,OAAO,EAAEP,YAAY,EAAEU,OAAO,CAACM,WAAW,EAAEE,SAAS,EAC3DL,IAAI,EAAEI,OAAO,EAAEC,SAAS,EAAEA,SAAS,EAAEV,KAAK,GAAGG,MAAM,CAAC;IACxD;IACA,IAAIQ,EAAE,GAAI,IAAI,IAAI,IAAI,KAAKf,IAAI,IAAI,IAAI,YAAYM,OAAO,GAAID,IAAI,GAAGH,IAAI;IACzE,OAAOR,KAAK,CAACqB,EAAE,EAAE,IAAI,EAAEN,IAAI,CAAC;EAC9B;EACA,OAAOH,OAAO;AAChB;AAEA,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}