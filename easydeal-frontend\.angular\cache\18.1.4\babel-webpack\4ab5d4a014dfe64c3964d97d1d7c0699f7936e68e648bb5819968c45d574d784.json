{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { SignupComponent } from './signup.component';\nimport { SignupRoutingModule } from './signup-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class SignupModule {\n  static ɵfac = function SignupModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SignupModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SignupModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, SignupRoutingModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SignupModule, {\n    declarations: [SignupComponent],\n    imports: [CommonModule, ReactiveFormsModule, FormsModule, RouterModule, SignupRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "RouterModule", "SignupComponent", "SignupRoutingModule", "SignupModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\fronLogin\\easydeal-frontend\\src\\app\\pages\\Auth\\signup\\signup.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\nimport { SignupComponent } from './signup.component';\nimport { SignupRoutingModule } from './signup-routing.module';\n\n@NgModule({\n  declarations: [\n    SignupComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    FormsModule,\n    RouterModule,\n    SignupRoutingModule\n  ]\n})\nexport class SignupModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,eAAe,QAAQ,oBAAoB;AACpD,SAASC,mBAAmB,QAAQ,yBAAyB;;AAc7D,OAAM,MAAOC,YAAY;;qCAAZA,YAAY;EAAA;;UAAZA;EAAY;;cAPrBN,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZE,mBAAmB;EAAA;;;2EAGVC,YAAY;IAAAC,YAAA,GAVrBH,eAAe;IAAAI,OAAA,GAGfR,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,YAAY,EACZE,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}