{"ast": null, "code": "/**\n * Converts `iterator` to an array.\n *\n * @private\n * @param {Object} iterator The iterator to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction iteratorToArray(iterator) {\n  var data,\n    result = [];\n  while (!(data = iterator.next()).done) {\n    result.push(data.value);\n  }\n  return result;\n}\nexport default iteratorToArray;", "map": {"version": 3, "names": ["iteratorToArray", "iterator", "data", "result", "next", "done", "push", "value"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_iteratorToArray.js"], "sourcesContent": ["/**\n * Converts `iterator` to an array.\n *\n * @private\n * @param {Object} iterator The iterator to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction iteratorToArray(iterator) {\n  var data,\n      result = [];\n\n  while (!(data = iterator.next()).done) {\n    result.push(data.value);\n  }\n  return result;\n}\n\nexport default iteratorToArray;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAeA,CAACC,QAAQ,EAAE;EACjC,IAAIC,IAAI;IACJC,MAAM,GAAG,EAAE;EAEf,OAAO,CAAC,CAACD,IAAI,GAAGD,QAAQ,CAACG,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAE;IACrCF,MAAM,CAACG,IAAI,CAACJ,IAAI,CAACK,KAAK,CAAC;EACzB;EACA,OAAOJ,MAAM;AACf;AAEA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}