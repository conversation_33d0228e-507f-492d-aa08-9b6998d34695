{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction SignupComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵelement(2, \"input\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 22);\n    i0.ɵɵelement(4, \"input\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22);\n    i0.ɵɵelement(6, \"input\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 22);\n    i0.ɵɵelement(8, \"input\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 22);\n    i0.ɵɵelement(10, \"input\", 27);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SignupComponent_div_31_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0627\\u062E\\u062A\\u0631\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SignupComponent_div_31_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u062C\\u0627\\u0631\\u064A \\u0627\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SignupComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"button\", 29);\n    i0.ɵɵtemplate(2, SignupComponent_div_31_span_2_Template, 2, 0, \"span\", 30)(3, SignupComponent_div_31_span_3_Template, 2, 0, \"span\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 31);\n    i0.ɵɵelement(5, \"i\", 32);\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7, \"\\u0644\\u062F\\u064A\\u0643 \\u062D\\u0633\\u0627\\u0628\\u061F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"a\", 33);\n    i0.ɵɵlistener(\"click\", function SignupComponent_div_31_Template_a_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵtext(9, \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062F\\u062E\\u0648\\u0644 \\u0645\\u0646 \\u0647\\u0646\\u0627\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"loading\", ctx_r1.isLoading);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || !ctx_r1.signupForm.valid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n  }\n}\nexport class SignupComponent {\n  fb;\n  router;\n  signupForm;\n  isLoading = false;\n  constructor(fb, router) {\n    this.fb = fb;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.initForm();\n  }\n  initForm() {\n    this.signupForm = this.fb.group({\n      // User Type Selection\n      userType: ['', [Validators.required]],\n      // Basic Information\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{11}$/)]],\n      // Account Setup\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      agreeToTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n      return {\n        passwordMismatch: true\n      };\n    }\n    return null;\n  }\n  selectUserType(userType) {\n    this.signupForm.patchValue({\n      userType\n    });\n  }\n  onSubmit() {\n    if (this.signupForm.valid) {\n      this.isLoading = true;\n      // Simulate API call\n      setTimeout(() => {\n        console.log('Form submitted:', this.signupForm.value);\n        this.isLoading = false;\n        // Navigate to login or dashboard\n        this.router.navigate(['/auth/signin']);\n      }, 2000);\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.signupForm.controls).forEach(key => {\n      const control = this.signupForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  goToLogin() {\n    this.router.navigate(['/auth/signin']);\n  }\n  static ɵfac = function SignupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SignupComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SignupComponent,\n    selectors: [[\"app-signup\"]],\n    decls: 32,\n    vars: 9,\n    consts: [[1, \"signup-container\"], [1, \"background-section\"], [1, \"logo-container\"], [1, \"logo-icon\"], [\"src\", \"assets/media/easydeallogos/home-logo.png\", \"alt\", \"Easy Deal Logo\", 1, \"logo-img\"], [1, \"logo-text\"], [1, \"logo-main\"], [1, \"form-section\"], [1, \"form-card\"], [1, \"step-content\"], [1, \"form-title\"], [1, \"form-subtitle\"], [1, \"form-description\"], [1, \"signup-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"user-type-buttons\"], [\"type\", \"button\", 1, \"user-type-btn\", 3, \"click\"], [1, \"fas\", \"fa-building\"], [1, \"fas\", \"fa-handshake\"], [1, \"fas\", \"fa-user\"], [\"class\", \"form-fields\", 4, \"ngIf\"], [\"class\", \"form-footer\", 4, \"ngIf\"], [1, \"form-fields\"], [1, \"form-group\"], [\"type\", \"text\", \"formControlName\", \"fullName\", \"placeholder\", \"\\u0627\\u0644\\u0627\\u0633\\u0645 \\u0643\\u0627\\u0645\\u0644\\u0627\\u064B\", 1, \"form-control\"], [\"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"\\u0627\\u0644\\u0628\\u0631\\u064A\\u062F \\u0627\\u0644\\u0625\\u0644\\u0643\\u062A\\u0631\\u0648\\u0646\\u064A\", 1, \"form-control\"], [\"type\", \"tel\", \"formControlName\", \"phone\", \"placeholder\", \"\\u0631\\u0642\\u0645 \\u0627\\u0644\\u0647\\u0627\\u062A\\u0641\", 1, \"form-control\"], [\"type\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"\\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\", 1, \"form-control\"], [\"type\", \"password\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"\\u062A\\u0623\\u0643\\u064A\\u062F \\u0643\\u0644\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0631\\u0648\\u0631\", 1, \"form-control\"], [1, \"form-footer\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"btn-submit\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"back-to-login\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"login-link\", 3, \"click\"]],\n    template: function SignupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"img\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵtext(7, \"EASY\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 6);\n        i0.ɵɵtext(9, \"DEAL\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"h2\", 10);\n        i0.ɵɵtext(14, \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0625\\u064A\\u0632\\u064A \\u062F\\u064A\\u0644\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"p\", 11);\n        i0.ɵɵtext(16, \"\\u062D\\u062F\\u062F \\u0637\\u0628\\u064A\\u0639\\u062A\\u0643 \\u0643\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"p\", 12);\n        i0.ɵɵtext(18, \" \\u0646\\u0642\\u062F\\u0645 \\u0644\\u0643 \\u0627\\u0644\\u062D\\u0633\\u0627\\u0628 \\u0627\\u0644\\u0630\\u064A \\u064A\\u0646\\u0627\\u0633\\u0628 \\u0637\\u0628\\u064A\\u0639\\u0629 \\u0639\\u0645\\u0644\\u0643 \\u0644\\u0644\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0641\\u064A \\u0627\\u0644\\u0645\\u0648\\u0642\\u0639 \\u0648\\u062A\\u062D\\u0642\\u064A\\u0642 \\u0623\\u0647\\u062F\\u0627\\u0641\\u0643 \\u0628\\u0633\\u0647\\u0648\\u0644\\u0629 \\u0639\\u0645\\u0644\\u0627\\u0621 \\u0648 \\u0645\\u0637\\u0648\\u0631\\u064A\\u0646 \\u064A\\u0645\\u0643\\u0646 \\u062A\\u062D\\u0642\\u064A\\u0642 \\u0623\\u0647\\u062F\\u0627\\u0641 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"form\", 13);\n        i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_19_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵelementStart(20, \"div\", 14)(21, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function SignupComponent_Template_button_click_21_listener() {\n          return ctx.selectUserType(\"developer\");\n        });\n        i0.ɵɵelement(22, \"i\", 16);\n        i0.ɵɵtext(23, \" \\u0645\\u0637\\u0648\\u0631 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function SignupComponent_Template_button_click_24_listener() {\n          return ctx.selectUserType(\"broker\");\n        });\n        i0.ɵɵelement(25, \"i\", 17);\n        i0.ɵɵtext(26, \" \\u0648\\u0633\\u064A\\u0637 \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function SignupComponent_Template_button_click_27_listener() {\n          return ctx.selectUserType(\"client\");\n        });\n        i0.ɵɵelement(28, \"i\", 18);\n        i0.ɵɵtext(29, \" \\u0639\\u0645\\u064A\\u0644 \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(30, SignupComponent_div_30_Template, 11, 0, \"div\", 19)(31, SignupComponent_div_31_Template, 10, 5, \"div\", 20);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        let tmp_1_0;\n        let tmp_2_0;\n        let tmp_3_0;\n        let tmp_4_0;\n        let tmp_5_0;\n        i0.ɵɵadvance(19);\n        i0.ɵɵproperty(\"formGroup\", ctx.signupForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"selected\", ((tmp_1_0 = ctx.signupForm.get(\"userType\")) == null ? null : tmp_1_0.value) === \"developer\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"selected\", ((tmp_2_0 = ctx.signupForm.get(\"userType\")) == null ? null : tmp_2_0.value) === \"broker\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"selected\", ((tmp_3_0 = ctx.signupForm.get(\"userType\")) == null ? null : tmp_3_0.value) === \"client\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx.signupForm.get(\"userType\")) == null ? null : tmp_4_0.value);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx.signupForm.get(\"userType\")) == null ? null : tmp_5_0.value);\n      }\n    },\n    dependencies: [i3.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n    styles: [\".signup-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  min-height: 100vh;\\n  direction: rtl;\\n  font-family: \\\"Cairo\\\", sans-serif;\\n}\\n\\n.background-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);\\n  background-image: url('EaseDealPage.png');\\n  background-size: cover;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n  position: relative;\\n  display: flex;\\n  align-items: flex-start;\\n  justify-content: flex-start;\\n  padding: 2rem;\\n}\\n.background-section[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(30, 58, 138, 0.7) 0%, rgba(59, 130, 246, 0.5) 50%, rgba(30, 64, 175, 0.7) 100%);\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.logo-container[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 0.5rem;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.logo-container[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%]   .logo-img[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n  object-fit: contain;\\n}\\n.logo-container[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n  color: white;\\n  font-family: \\\"Arial\\\", sans-serif;\\n  font-weight: 900;\\n  font-size: 3rem;\\n  line-height: 0.9;\\n  text-align: left;\\n}\\n.logo-container[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%]   .logo-main[_ngcontent-%COMP%] {\\n  display: block;\\n  letter-spacing: 2px;\\n}\\n\\n.form-section[_ngcontent-%COMP%] {\\n  flex: 0 0 500px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 2rem;\\n  background: #f8fafc;\\n}\\n\\n.form-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 20px;\\n  padding: 3rem 2.5rem;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\\n  width: 100%;\\n  max-width: 450px;\\n  text-align: center;\\n}\\n\\n.step-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.form-title[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  font-weight: 700;\\n  color: #1e293b;\\n  margin-bottom: 0.5rem;\\n  line-height: 1.3;\\n}\\n\\n.form-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #475569;\\n  margin-bottom: 1rem;\\n}\\n\\n.form-description[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  color: #64748b;\\n  line-height: 1.6;\\n  margin-bottom: 2rem;\\n}\\n\\n.user-type-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 2rem;\\n  flex-wrap: wrap;\\n  justify-content: center;\\n}\\n\\n.user-type-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 100px;\\n  padding: 1rem 0.5rem;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 12px;\\n  background: white;\\n  color: #64748b;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.user-type-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin-bottom: 0.25rem;\\n}\\n.user-type-btn[_ngcontent-%COMP%]:hover {\\n  border-color: #3b82f6;\\n  color: #3b82f6;\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);\\n}\\n.user-type-btn.selected[_ngcontent-%COMP%] {\\n  border-color: #3b82f6;\\n  background: #3b82f6;\\n  color: white;\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);\\n}\\n\\n.signup-form[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.form-fields[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  text-align: right;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  color: #374151;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.95rem;\\n}\\n\\n.input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.875rem 1rem;\\n  padding-left: 3rem;\\n  border: 2px solid #e5e7eb;\\n  border-radius: 10px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n  background: #f9fafb;\\n}\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b82f6;\\n  background: white;\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n.form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #9ca3af;\\n  font-size: 0.9rem;\\n}\\n\\n.input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #9ca3af;\\n  font-size: 1.1rem;\\n}\\n\\n.form-footer[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1rem;\\n  border: none;\\n  border-radius: 12px;\\n  font-weight: 600;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  margin-bottom: 1.5rem;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #3b82f6;\\n  color: white;\\n}\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #2563eb;\\n  transform: translateY(-1px);\\n  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);\\n}\\n.btn-primary.disabled[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%]:disabled {\\n  background: #d1d5db;\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n.btn-primary.loading[_ngcontent-%COMP%] {\\n  background: #6b7280;\\n  cursor: wait;\\n}\\n\\n.back-to-login[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #6b7280;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n.back-to-login[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .login-link[_ngcontent-%COMP%] {\\n  color: #3b82f6;\\n  text-decoration: none;\\n  font-weight: 600;\\n  cursor: pointer;\\n}\\n.back-to-login[_ngcontent-%COMP%]   .login-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n@media (max-width: 768px) {\\n  .signup-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .background-section[_ngcontent-%COMP%] {\\n    flex: 0 0 200px;\\n    padding: 1rem;\\n  }\\n  .form-section[_ngcontent-%COMP%] {\\n    flex: 1;\\n    padding: 1rem;\\n  }\\n  .form-card[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n  }\\n  .user-type-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n  .user-type-btn[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵtemplate", "SignupComponent_div_31_span_2_Template", "SignupComponent_div_31_span_3_Template", "ɵɵlistener", "SignupComponent_div_31_Template_a_click_8_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "goToLogin", "ɵɵadvance", "ɵɵclassProp", "isLoading", "ɵɵproperty", "signupForm", "valid", "SignupComponent", "fb", "router", "constructor", "ngOnInit", "initForm", "group", "userType", "required", "fullName", "<PERSON><PERSON><PERSON><PERSON>", "email", "phone", "pattern", "password", "confirmPassword", "agreeToTerms", "requiredTrue", "validators", "passwordMatchValidator", "form", "get", "value", "setErrors", "passwordMismatch", "selectUserType", "patchValue", "onSubmit", "setTimeout", "console", "log", "navigate", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "SignupComponent_Template_form_ngSubmit_19_listener", "SignupComponent_Template_button_click_21_listener", "SignupComponent_Template_button_click_24_listener", "SignupComponent_Template_button_click_27_listener", "SignupComponent_div_30_Template", "SignupComponent_div_31_Template", "tmp_1_0", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\fronLogin\\easydeal-frontend\\src\\app\\pages\\Auth\\signup\\signup.component.ts", "C:\\Users\\<USER>\\Desktop\\taskes\\fronLogin\\easydeal-frontend\\src\\app\\pages\\Auth\\signup\\signup.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})\nexport class SignupComponent implements OnInit {\n  signupForm: FormGroup;\n  isLoading = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  initForm() {\n    this.signupForm = this.fb.group({\n      // User Type Selection\n      userType: ['', [Validators.required]],\n\n      // Basic Information\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{11}$/)]],\n\n      // Account Setup\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      agreeToTerms: [false, [Validators.requiredTrue]]\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n      return { passwordMismatch: true };\n    }\n\n    return null;\n  }\n\n  selectUserType(userType: string) {\n    this.signupForm.patchValue({ userType });\n  }\n\n  onSubmit() {\n    if (this.signupForm.valid) {\n      this.isLoading = true;\n\n      // Simulate API call\n      setTimeout(() => {\n        console.log('Form submitted:', this.signupForm.value);\n        this.isLoading = false;\n        // Navigate to login or dashboard\n        this.router.navigate(['/auth/signin']);\n      }, 2000);\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  private markFormGroupTouched() {\n    Object.keys(this.signupForm.controls).forEach(key => {\n      const control = this.signupForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  goToLogin() {\n    this.router.navigate(['/auth/signin']);\n  }\n}\n", "<!-- Main Container -->\n<div class=\"signup-container\">\n  <!-- Background Image -->\n  <div class=\"background-section\">\n    <!-- Logo -->\n    <div class=\"logo-container\">\n      <div class=\"logo-icon\">\n        <img src=\"assets/media/easydeallogos/home-logo.png\" alt=\"Easy Deal Logo\" class=\"logo-img\">\n      </div>\n      <div class=\"logo-text\">\n        <div class=\"logo-main\">EASY</div>\n        <div class=\"logo-main\">DEAL</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Form Section -->\n  <div class=\"form-section\">\n    <div class=\"form-card\">\n      <!-- Main Form Content -->\n      <div class=\"step-content\">\n        <h2 class=\"form-title\">مرحباً بك في إيزي ديل</h2>\n        <p class=\"form-subtitle\">حدد طبيعتك كمستخدم</p>\n        <p class=\"form-description\">\n          نقدم لك الحساب الذي يناسب طبيعة عملك للتسجيل في الموقع وتحقيق أهدافك بسهولة\n          عملاء و مطورين يمكن تحقيق أهداف\n        </p>\n\n        <form [formGroup]=\"signupForm\" (ngSubmit)=\"onSubmit()\" class=\"signup-form\">\n          <!-- User Type Selection -->\n          <div class=\"user-type-buttons\">\n            <button type=\"button\" class=\"user-type-btn\"\n              [class.selected]=\"signupForm.get('userType')?.value === 'developer'\"\n              (click)=\"selectUserType('developer')\">\n              <i class=\"fas fa-building\"></i>\n              مطور\n            </button>\n\n            <button type=\"button\" class=\"user-type-btn\"\n              [class.selected]=\"signupForm.get('userType')?.value === 'broker'\" (click)=\"selectUserType('broker')\">\n              <i class=\"fas fa-handshake\"></i>\n              وسيط\n            </button>\n\n            <button type=\"button\" class=\"user-type-btn\"\n              [class.selected]=\"signupForm.get('userType')?.value === 'client'\" (click)=\"selectUserType('client')\">\n              <i class=\"fas fa-user\"></i>\n              عميل\n            </button>\n          </div>\n\n          <!-- Form Fields -->\n          <div class=\"form-fields\" *ngIf=\"signupForm.get('userType')?.value\">\n            <div class=\"form-group\">\n              <input type=\"text\" class=\"form-control\" formControlName=\"fullName\" placeholder=\"الاسم كاملاً\">\n            </div>\n\n            <div class=\"form-group\">\n              <input type=\"email\" class=\"form-control\" formControlName=\"email\" placeholder=\"البريد الإلكتروني\">\n            </div>\n\n            <div class=\"form-group\">\n              <input type=\"tel\" class=\"form-control\" formControlName=\"phone\" placeholder=\"رقم الهاتف\">\n            </div>\n\n            <div class=\"form-group\">\n              <input type=\"password\" class=\"form-control\" formControlName=\"password\" placeholder=\"كلمة المرور\">\n            </div>\n\n            <div class=\"form-group\">\n              <input type=\"password\" class=\"form-control\" formControlName=\"confirmPassword\"\n                placeholder=\"تأكيد كلمة المرور\">\n            </div>\n          </div>\n\n          <!-- Submit Button -->\n          <div class=\"form-footer\" *ngIf=\"signupForm.get('userType')?.value\">\n            <button type=\"submit\" class=\"btn btn-primary btn-submit\" [class.loading]=\"isLoading\"\n              [disabled]=\"isLoading || !signupForm.valid\">\n              <span *ngIf=\"!isLoading\">اختر</span>\n              <span *ngIf=\"isLoading\">جاري التسجيل...</span>\n            </button>\n\n            <div class=\"back-to-login\">\n              <i class=\"fas fa-arrow-right\"></i>\n              <span>لديك حساب؟ </span>\n              <a (click)=\"goToLogin()\" class=\"login-link\">تسجيل الدخول من هنا</a>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;ICoDvDC,EADF,CAAAC,cAAA,cAAmE,cACzC;IACtBD,EAAA,CAAAE,SAAA,gBAA8F;IAChGF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,gBAAiG;IACnGF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,gBAAwF;IAC1FF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,gBAAiG;IACnGF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,iBACkC;IAEtCF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAMFH,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAI,MAAA,+BAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACpCH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAI,MAAA,6EAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAHhDH,EADF,CAAAC,cAAA,cAAmE,iBAEnB;IAE5CD,EADA,CAAAK,UAAA,IAAAC,sCAAA,mBAAyB,IAAAC,sCAAA,mBACD;IAC1BP,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAE,SAAA,YAAkC;IAClCF,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAI,MAAA,+DAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACxBH,EAAA,CAAAC,cAAA,YAA4C;IAAzCD,EAAA,CAAAQ,UAAA,mBAAAC,mDAAA;MAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IAAoBf,EAAA,CAAAI,MAAA,0GAAmB;IAEnEJ,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;;;;IAXqDH,EAAA,CAAAgB,SAAA,EAA2B;IAA3BhB,EAAA,CAAAiB,WAAA,YAAAL,MAAA,CAAAM,SAAA,CAA2B;IAClFlB,EAAA,CAAAmB,UAAA,aAAAP,MAAA,CAAAM,SAAA,KAAAN,MAAA,CAAAQ,UAAA,CAAAC,KAAA,CAA2C;IACpCrB,EAAA,CAAAgB,SAAA,EAAgB;IAAhBhB,EAAA,CAAAmB,UAAA,UAAAP,MAAA,CAAAM,SAAA,CAAgB;IAChBlB,EAAA,CAAAgB,SAAA,EAAe;IAAfhB,EAAA,CAAAmB,UAAA,SAAAP,MAAA,CAAAM,SAAA,CAAe;;;ADvEpC,OAAM,MAAOI,eAAe;EAKhBC,EAAA;EACAC,MAAA;EALVJ,UAAU;EACVF,SAAS,GAAG,KAAK;EAEjBO,YACUF,EAAe,EACfC,MAAc;IADd,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHE,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAA,QAAQA,CAAA;IACN,IAAI,CAACP,UAAU,GAAG,IAAI,CAACG,EAAE,CAACK,KAAK,CAAC;MAC9B;MACAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC9B,UAAU,CAAC+B,QAAQ,CAAC,CAAC;MAErC;MACAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACiC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACkC,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACoC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAErE;MACAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAAC+B,QAAQ,EAAE/B,UAAU,CAACiC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DK,eAAe,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAAC+B,QAAQ,CAAC,CAAC;MAC5CQ,YAAY,EAAE,CAAC,KAAK,EAAE,CAACvC,UAAU,CAACwC,YAAY,CAAC;KAChD,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,MAAMN,QAAQ,GAAGM,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMN,eAAe,GAAGK,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAIP,QAAQ,IAAIC,eAAe,IAAID,QAAQ,CAACQ,KAAK,KAAKP,eAAe,CAACO,KAAK,EAAE;MAC3EP,eAAe,CAACQ,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;MACrD,OAAO;QAAEA,gBAAgB,EAAE;MAAI,CAAE;IACnC;IAEA,OAAO,IAAI;EACb;EAEAC,cAAcA,CAAClB,QAAgB;IAC7B,IAAI,CAACT,UAAU,CAAC4B,UAAU,CAAC;MAAEnB;IAAQ,CAAE,CAAC;EAC1C;EAEAoB,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC7B,UAAU,CAACC,KAAK,EAAE;MACzB,IAAI,CAACH,SAAS,GAAG,IAAI;MAErB;MACAgC,UAAU,CAAC,MAAK;QACdC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAChC,UAAU,CAACwB,KAAK,CAAC;QACrD,IAAI,CAAC1B,SAAS,GAAG,KAAK;QACtB;QACA,IAAI,CAACM,MAAM,CAAC6B,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL,IAAI,CAACC,oBAAoB,EAAE;IAC7B;EACF;EAEQA,oBAAoBA,CAAA;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpC,UAAU,CAACqC,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMC,OAAO,GAAG,IAAI,CAACxC,UAAU,CAACuB,GAAG,CAACgB,GAAG,CAAC;MACxCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA9C,SAASA,CAAA;IACP,IAAI,CAACS,MAAM,CAAC6B,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;;qCAvEW/B,eAAe,EAAAtB,EAAA,CAAA8D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhE,EAAA,CAAA8D,iBAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;;UAAf5C,eAAe;IAAA6C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCHtBzE,EALN,CAAAC,cAAA,aAA8B,aAEI,aAEF,aACH;QACrBD,EAAA,CAAAE,SAAA,aAA0F;QAC5FF,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAuB,aACE;QAAAD,EAAA,CAAAI,MAAA,WAAI;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QACjCH,EAAA,CAAAC,cAAA,aAAuB;QAAAD,EAAA,CAAAI,MAAA,WAAI;QAGjCJ,EAHiC,CAAAG,YAAA,EAAM,EAC7B,EACF,EACF;QAOAH,EAJN,CAAAC,cAAA,cAA0B,cACD,cAEK,cACD;QAAAD,EAAA,CAAAI,MAAA,kHAAqB;QAAAJ,EAAA,CAAAG,YAAA,EAAK;QACjDH,EAAA,CAAAC,cAAA,aAAyB;QAAAD,EAAA,CAAAI,MAAA,0GAAkB;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAC/CH,EAAA,CAAAC,cAAA,aAA4B;QAC1BD,EAAA,CAAAI,MAAA,kjBAEF;QAAAJ,EAAA,CAAAG,YAAA,EAAI;QAEJH,EAAA,CAAAC,cAAA,gBAA2E;QAA5CD,EAAA,CAAAQ,UAAA,sBAAAmE,mDAAA;UAAA,OAAYD,GAAA,CAAAzB,QAAA,EAAU;QAAA,EAAC;QAGlDjD,EADF,CAAAC,cAAA,eAA+B,kBAGW;QAAtCD,EAAA,CAAAQ,UAAA,mBAAAoE,kDAAA;UAAA,OAASF,GAAA,CAAA3B,cAAA,CAAe,WAAW,CAAC;QAAA,EAAC;QACrC/C,EAAA,CAAAE,SAAA,aAA+B;QAC/BF,EAAA,CAAAI,MAAA,kCACF;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAETH,EAAA,CAAAC,cAAA,kBACuG;QAAnCD,EAAA,CAAAQ,UAAA,mBAAAqE,kDAAA;UAAA,OAASH,GAAA,CAAA3B,cAAA,CAAe,QAAQ,CAAC;QAAA,EAAC;QACpG/C,EAAA,CAAAE,SAAA,aAAgC;QAChCF,EAAA,CAAAI,MAAA,kCACF;QAAAJ,EAAA,CAAAG,YAAA,EAAS;QAETH,EAAA,CAAAC,cAAA,kBACuG;QAAnCD,EAAA,CAAAQ,UAAA,mBAAAsE,kDAAA;UAAA,OAASJ,GAAA,CAAA3B,cAAA,CAAe,QAAQ,CAAC;QAAA,EAAC;QACpG/C,EAAA,CAAAE,SAAA,aAA2B;QAC3BF,EAAA,CAAAI,MAAA,kCACF;QACFJ,EADE,CAAAG,YAAA,EAAS,EACL;QA2BNH,EAxBA,CAAAK,UAAA,KAAA0E,+BAAA,mBAAmE,KAAAC,+BAAA,mBAwBA;QAiB7EhF,EAJQ,CAAAG,YAAA,EAAO,EACH,EACF,EACF,EACF;;;;;;;;QAjEQH,EAAA,CAAAgB,SAAA,IAAwB;QAAxBhB,EAAA,CAAAmB,UAAA,cAAAuD,GAAA,CAAAtD,UAAA,CAAwB;QAIxBpB,EAAA,CAAAgB,SAAA,GAAoE;QAApEhB,EAAA,CAAAiB,WAAA,eAAAgE,OAAA,GAAAP,GAAA,CAAAtD,UAAA,CAAAuB,GAAA,+BAAAsC,OAAA,CAAArC,KAAA,kBAAoE;QAOpE5C,EAAA,CAAAgB,SAAA,GAAiE;QAAjEhB,EAAA,CAAAiB,WAAA,eAAAiE,OAAA,GAAAR,GAAA,CAAAtD,UAAA,CAAAuB,GAAA,+BAAAuC,OAAA,CAAAtC,KAAA,eAAiE;QAMjE5C,EAAA,CAAAgB,SAAA,GAAiE;QAAjEhB,EAAA,CAAAiB,WAAA,eAAAkE,OAAA,GAAAT,GAAA,CAAAtD,UAAA,CAAAuB,GAAA,+BAAAwC,OAAA,CAAAvC,KAAA,eAAiE;QAO3C5C,EAAA,CAAAgB,SAAA,GAAuC;QAAvChB,EAAA,CAAAmB,UAAA,UAAAiE,OAAA,GAAAV,GAAA,CAAAtD,UAAA,CAAAuB,GAAA,+BAAAyC,OAAA,CAAAxC,KAAA,CAAuC;QAwBvC5C,EAAA,CAAAgB,SAAA,EAAuC;QAAvChB,EAAA,CAAAmB,UAAA,UAAAkE,OAAA,GAAAX,GAAA,CAAAtD,UAAA,CAAAuB,GAAA,+BAAA0C,OAAA,CAAAzC,KAAA,CAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}