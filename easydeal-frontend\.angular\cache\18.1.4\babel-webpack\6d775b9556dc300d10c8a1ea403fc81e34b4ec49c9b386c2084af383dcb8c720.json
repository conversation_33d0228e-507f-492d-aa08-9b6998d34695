{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { SignupRoutingModule } from './signup-routing.module';\nimport { SignupComponent } from './signup.component';\nimport * as i0 from \"@angular/core\";\nexport class AuthModule {\n  static ɵfac = function AuthModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, SignupRoutingModule, FormsModule, ReactiveFormsModule, HttpClientModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [SignupComponent],\n    imports: [CommonModule, SignupRoutingModule, FormsModule, ReactiveFormsModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "FormsModule", "HttpClientModule", "SignupRoutingModule", "SignupComponent", "AuthModule", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\fronLogin\\easydeal-frontend\\src\\app\\pages\\Auth\\SignUp\\signup.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { SignupRoutingModule } from './signup-routing.module';\nimport { SignupComponent } from './signup.component';\n\n@NgModule({\n  declarations: [\n    SignupComponent,\n  ],\n  imports: [\n    CommonModule,\n    SignupRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    HttpClientModule,\n  ],\n})\nexport class AuthModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,oBAAoB;;AAcpD,OAAM,MAAOC,UAAU;;qCAAVA,UAAU;EAAA;;UAAVA;EAAU;;cAPnBN,YAAY,EACZI,mBAAmB,EACnBF,WAAW,EACXD,mBAAmB,EACnBE,gBAAgB;EAAA;;;2EAGPG,UAAU;IAAAC,YAAA,GAVnBF,eAAe;IAAAG,OAAA,GAGfR,YAAY,EACZI,mBAAmB,EACnBF,WAAW,EACXD,mBAAmB,EACnBE,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}