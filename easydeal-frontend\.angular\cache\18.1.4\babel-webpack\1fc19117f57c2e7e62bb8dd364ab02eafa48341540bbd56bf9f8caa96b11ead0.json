{"ast": null, "code": "import WeakMap from './_WeakMap.js';\n\n/** Used to store function metadata. */\nvar metaMap = WeakMap && new WeakMap();\nexport default metaMap;", "map": {"version": 3, "names": ["WeakMap", "metaMap"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_metaMap.js"], "sourcesContent": ["import WeakMap from './_WeakMap.js';\n\n/** Used to store function metadata. */\nvar metaMap = WeakMap && new WeakMap;\n\nexport default metaMap;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;;AAEnC;AACA,IAAIC,OAAO,GAAGD,OAAO,IAAI,IAAIA,OAAO,CAAD,CAAC;AAEpC,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}