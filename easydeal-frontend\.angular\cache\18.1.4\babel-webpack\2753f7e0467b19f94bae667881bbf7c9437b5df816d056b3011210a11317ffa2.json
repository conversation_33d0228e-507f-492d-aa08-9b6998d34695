{"ast": null, "code": "/** Used to match template delimiters. */\nvar reInterpolate = /<%=([\\s\\S]+?)%>/g;\nexport default reInterpolate;", "map": {"version": 3, "names": ["reInterpolate"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_reInterpolate.js"], "sourcesContent": ["/** Used to match template delimiters. */\nvar reInterpolate = /<%=([\\s\\S]+?)%>/g;\n\nexport default reInterpolate;\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAG,kBAAkB;AAEtC,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}