export interface PropertyData {
  // Step 1: Location Information
  unitType: string;
  city: string;
  area: string;

  detailedAddress: string;
  googleMapsLink: string;

  // Step 2: Unit Information
  propertyNumber: string;
  apartmentNumber: string;
  floor: string;
  apartmentArea: string | number;
  roomsCount: string | number;
  bathroomsCount: string | number;
  apartmentLocation: string;
  apartmentView: string;
  finishingStatus: string;
  deliveryStatus: string;
  additionalAmenities: string;

  // Step 3: Financial Information
  paymentSystem: string;

  // Step 4: Media & Documents
  projectUnitImage: string;
  projectLayout: string;
  projectUnitPlan: string;
  projectVideos: string;

  // Step 5: Owner Information
  ownerName: string;
  ownerPhone: string;
  legalStatus: string;
}
