{"ast": null, "code": "import baseSortedIndex from './_baseSortedIndex.js';\n\n/**\n * This method is like `_.sortedIndex` except that it returns the highest\n * index at which `value` should be inserted into `array` in order to\n * maintain its sort order.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n * @example\n *\n * _.sortedLastIndex([4, 5, 5, 5, 6], 5);\n * // => 4\n */\nfunction sortedLastIndex(array, value) {\n  return baseSortedIndex(array, value, true);\n}\nexport default sortedLastIndex;", "map": {"version": 3, "names": ["baseSortedIndex", "sortedLastIndex", "array", "value"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/sortedLastIndex.js"], "sourcesContent": ["import baseSortedIndex from './_baseSortedIndex.js';\n\n/**\n * This method is like `_.sortedIndex` except that it returns the highest\n * index at which `value` should be inserted into `array` in order to\n * maintain its sort order.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Array\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n * @example\n *\n * _.sortedLastIndex([4, 5, 5, 5, 6], 5);\n * // => 4\n */\nfunction sortedLastIndex(array, value) {\n  return baseSortedIndex(array, value, true);\n}\n\nexport default sortedLastIndex;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACrC,OAAOH,eAAe,CAACE,KAAK,EAAEC,KAAK,EAAE,IAAI,CAAC;AAC5C;AAEA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}