{"ast": null, "code": "import assignMergeValue from './_assignMergeValue.js';\nimport cloneBuffer from './_cloneBuffer.js';\nimport cloneTypedArray from './_cloneTypedArray.js';\nimport copyArray from './_copyArray.js';\nimport initCloneObject from './_initCloneObject.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport isBuffer from './isBuffer.js';\nimport isFunction from './isFunction.js';\nimport isObject from './isObject.js';\nimport isPlainObject from './isPlainObject.js';\nimport isTypedArray from './isTypedArray.js';\nimport safeGet from './_safeGet.js';\nimport toPlainObject from './toPlainObject.js';\n\n/**\n * A specialized version of `baseMerge` for arrays and objects which performs\n * deep merges and tracks traversed objects enabling objects with circular\n * references to be merged.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {string} key The key of the value to merge.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} mergeFunc The function to merge values.\n * @param {Function} [customizer] The function to customize assigned values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {\n  var objValue = safeGet(object, key),\n    srcValue = safeGet(source, key),\n    stacked = stack.get(srcValue);\n  if (stacked) {\n    assignMergeValue(object, key, stacked);\n    return;\n  }\n  var newValue = customizer ? customizer(objValue, srcValue, key + '', object, source, stack) : undefined;\n  var isCommon = newValue === undefined;\n  if (isCommon) {\n    var isArr = isArray(srcValue),\n      isBuff = !isArr && isBuffer(srcValue),\n      isTyped = !isArr && !isBuff && isTypedArray(srcValue);\n    newValue = srcValue;\n    if (isArr || isBuff || isTyped) {\n      if (isArray(objValue)) {\n        newValue = objValue;\n      } else if (isArrayLikeObject(objValue)) {\n        newValue = copyArray(objValue);\n      } else if (isBuff) {\n        isCommon = false;\n        newValue = cloneBuffer(srcValue, true);\n      } else if (isTyped) {\n        isCommon = false;\n        newValue = cloneTypedArray(srcValue, true);\n      } else {\n        newValue = [];\n      }\n    } else if (isPlainObject(srcValue) || isArguments(srcValue)) {\n      newValue = objValue;\n      if (isArguments(objValue)) {\n        newValue = toPlainObject(objValue);\n      } else if (!isObject(objValue) || isFunction(objValue)) {\n        newValue = initCloneObject(srcValue);\n      }\n    } else {\n      isCommon = false;\n    }\n  }\n  if (isCommon) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, newValue);\n    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);\n    stack['delete'](srcValue);\n  }\n  assignMergeValue(object, key, newValue);\n}\nexport default baseMergeDeep;", "map": {"version": 3, "names": ["assignMergeValue", "<PERSON><PERSON><PERSON><PERSON>", "cloneTypedArray", "copyArray", "initCloneObject", "isArguments", "isArray", "isArrayLikeObject", "<PERSON><PERSON><PERSON><PERSON>", "isFunction", "isObject", "isPlainObject", "isTypedArray", "safeGet", "toPlainObject", "baseMergeDeep", "object", "source", "key", "srcIndex", "mergeFunc", "customizer", "stack", "objValue", "srcValue", "stacked", "get", "newValue", "undefined", "isCommon", "isArr", "isBuff", "isTyped", "set"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_baseMergeDeep.js"], "sourcesContent": ["import assignMergeValue from './_assignMergeValue.js';\nimport cloneBuffer from './_cloneBuffer.js';\nimport cloneTypedArray from './_cloneTypedArray.js';\nimport copyArray from './_copyArray.js';\nimport initCloneObject from './_initCloneObject.js';\nimport isArguments from './isArguments.js';\nimport isArray from './isArray.js';\nimport isArrayLikeObject from './isArrayLikeObject.js';\nimport isBuffer from './isBuffer.js';\nimport isFunction from './isFunction.js';\nimport isObject from './isObject.js';\nimport isPlainObject from './isPlainObject.js';\nimport isTypedArray from './isTypedArray.js';\nimport safeGet from './_safeGet.js';\nimport toPlainObject from './toPlainObject.js';\n\n/**\n * A specialized version of `baseMerge` for arrays and objects which performs\n * deep merges and tracks traversed objects enabling objects with circular\n * references to be merged.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @param {string} key The key of the value to merge.\n * @param {number} srcIndex The index of `source`.\n * @param {Function} mergeFunc The function to merge values.\n * @param {Function} [customizer] The function to customize assigned values.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n */\nfunction baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {\n  var objValue = safeGet(object, key),\n      srcValue = safeGet(source, key),\n      stacked = stack.get(srcValue);\n\n  if (stacked) {\n    assignMergeValue(object, key, stacked);\n    return;\n  }\n  var newValue = customizer\n    ? customizer(objValue, srcValue, (key + ''), object, source, stack)\n    : undefined;\n\n  var isCommon = newValue === undefined;\n\n  if (isCommon) {\n    var isArr = isArray(srcValue),\n        isBuff = !isArr && isBuffer(srcValue),\n        isTyped = !isArr && !isBuff && isTypedArray(srcValue);\n\n    newValue = srcValue;\n    if (isArr || isBuff || isTyped) {\n      if (isArray(objValue)) {\n        newValue = objValue;\n      }\n      else if (isArrayLikeObject(objValue)) {\n        newValue = copyArray(objValue);\n      }\n      else if (isBuff) {\n        isCommon = false;\n        newValue = cloneBuffer(srcValue, true);\n      }\n      else if (isTyped) {\n        isCommon = false;\n        newValue = cloneTypedArray(srcValue, true);\n      }\n      else {\n        newValue = [];\n      }\n    }\n    else if (isPlainObject(srcValue) || isArguments(srcValue)) {\n      newValue = objValue;\n      if (isArguments(objValue)) {\n        newValue = toPlainObject(objValue);\n      }\n      else if (!isObject(objValue) || isFunction(objValue)) {\n        newValue = initCloneObject(srcValue);\n      }\n    }\n    else {\n      isCommon = false;\n    }\n  }\n  if (isCommon) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, newValue);\n    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);\n    stack['delete'](srcValue);\n  }\n  assignMergeValue(object, key, newValue);\n}\n\nexport default baseMergeDeep;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,aAAa,MAAM,oBAAoB;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,KAAK,EAAE;EAClF,IAAIC,QAAQ,GAAGV,OAAO,CAACG,MAAM,EAAEE,GAAG,CAAC;IAC/BM,QAAQ,GAAGX,OAAO,CAACI,MAAM,EAAEC,GAAG,CAAC;IAC/BO,OAAO,GAAGH,KAAK,CAACI,GAAG,CAACF,QAAQ,CAAC;EAEjC,IAAIC,OAAO,EAAE;IACXzB,gBAAgB,CAACgB,MAAM,EAAEE,GAAG,EAAEO,OAAO,CAAC;IACtC;EACF;EACA,IAAIE,QAAQ,GAAGN,UAAU,GACrBA,UAAU,CAACE,QAAQ,EAAEC,QAAQ,EAAGN,GAAG,GAAG,EAAE,EAAGF,MAAM,EAAEC,MAAM,EAAEK,KAAK,CAAC,GACjEM,SAAS;EAEb,IAAIC,QAAQ,GAAGF,QAAQ,KAAKC,SAAS;EAErC,IAAIC,QAAQ,EAAE;IACZ,IAAIC,KAAK,GAAGxB,OAAO,CAACkB,QAAQ,CAAC;MACzBO,MAAM,GAAG,CAACD,KAAK,IAAItB,QAAQ,CAACgB,QAAQ,CAAC;MACrCQ,OAAO,GAAG,CAACF,KAAK,IAAI,CAACC,MAAM,IAAInB,YAAY,CAACY,QAAQ,CAAC;IAEzDG,QAAQ,GAAGH,QAAQ;IACnB,IAAIM,KAAK,IAAIC,MAAM,IAAIC,OAAO,EAAE;MAC9B,IAAI1B,OAAO,CAACiB,QAAQ,CAAC,EAAE;QACrBI,QAAQ,GAAGJ,QAAQ;MACrB,CAAC,MACI,IAAIhB,iBAAiB,CAACgB,QAAQ,CAAC,EAAE;QACpCI,QAAQ,GAAGxB,SAAS,CAACoB,QAAQ,CAAC;MAChC,CAAC,MACI,IAAIQ,MAAM,EAAE;QACfF,QAAQ,GAAG,KAAK;QAChBF,QAAQ,GAAG1B,WAAW,CAACuB,QAAQ,EAAE,IAAI,CAAC;MACxC,CAAC,MACI,IAAIQ,OAAO,EAAE;QAChBH,QAAQ,GAAG,KAAK;QAChBF,QAAQ,GAAGzB,eAAe,CAACsB,QAAQ,EAAE,IAAI,CAAC;MAC5C,CAAC,MACI;QACHG,QAAQ,GAAG,EAAE;MACf;IACF,CAAC,MACI,IAAIhB,aAAa,CAACa,QAAQ,CAAC,IAAInB,WAAW,CAACmB,QAAQ,CAAC,EAAE;MACzDG,QAAQ,GAAGJ,QAAQ;MACnB,IAAIlB,WAAW,CAACkB,QAAQ,CAAC,EAAE;QACzBI,QAAQ,GAAGb,aAAa,CAACS,QAAQ,CAAC;MACpC,CAAC,MACI,IAAI,CAACb,QAAQ,CAACa,QAAQ,CAAC,IAAId,UAAU,CAACc,QAAQ,CAAC,EAAE;QACpDI,QAAQ,GAAGvB,eAAe,CAACoB,QAAQ,CAAC;MACtC;IACF,CAAC,MACI;MACHK,QAAQ,GAAG,KAAK;IAClB;EACF;EACA,IAAIA,QAAQ,EAAE;IACZ;IACAP,KAAK,CAACW,GAAG,CAACT,QAAQ,EAAEG,QAAQ,CAAC;IAC7BP,SAAS,CAACO,QAAQ,EAAEH,QAAQ,EAAEL,QAAQ,EAAEE,UAAU,EAAEC,KAAK,CAAC;IAC1DA,KAAK,CAAC,QAAQ,CAAC,CAACE,QAAQ,CAAC;EAC3B;EACAxB,gBAAgB,CAACgB,MAAM,EAAEE,GAAG,EAAES,QAAQ,CAAC;AACzC;AAEA,eAAeZ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}