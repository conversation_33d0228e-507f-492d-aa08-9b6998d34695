{"ast": null, "code": "import composeArgs from './_composeArgs.js';\nimport composeArgsRight from './_composeArgsRight.js';\nimport countHolders from './_countHolders.js';\nimport createCtor from './_createCtor.js';\nimport createRecurry from './_createRecurry.js';\nimport getHolder from './_getHolder.js';\nimport reorder from './_reorder.js';\nimport replaceHolders from './_replaceHolders.js';\nimport root from './_root.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n  WRAP_BIND_KEY_FLAG = 2,\n  WRAP_CURRY_FLAG = 8,\n  WRAP_CURRY_RIGHT_FLAG = 16,\n  WRAP_ARY_FLAG = 128,\n  WRAP_FLIP_FLAG = 512;\n\n/**\n * Creates a function that wraps `func` to invoke it with optional `this`\n * binding of `thisArg`, partial application, and currying.\n *\n * @private\n * @param {Function|string} func The function or method name to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {*} [thisArg] The `this` binding of `func`.\n * @param {Array} [partials] The arguments to prepend to those provided to\n *  the new function.\n * @param {Array} [holders] The `partials` placeholder indexes.\n * @param {Array} [partialsRight] The arguments to append to those provided\n *  to the new function.\n * @param {Array} [holdersRight] The `partialsRight` placeholder indexes.\n * @param {Array} [argPos] The argument positions of the new function.\n * @param {number} [ary] The arity cap of `func`.\n * @param {number} [arity] The arity of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createHybrid(func, bitmask, thisArg, partials, holders, partialsRight, holdersRight, argPos, ary, arity) {\n  var isAry = bitmask & WRAP_ARY_FLAG,\n    isBind = bitmask & WRAP_BIND_FLAG,\n    isBindKey = bitmask & WRAP_BIND_KEY_FLAG,\n    isCurried = bitmask & (WRAP_CURRY_FLAG | WRAP_CURRY_RIGHT_FLAG),\n    isFlip = bitmask & WRAP_FLIP_FLAG,\n    Ctor = isBindKey ? undefined : createCtor(func);\n  function wrapper() {\n    var length = arguments.length,\n      args = Array(length),\n      index = length;\n    while (index--) {\n      args[index] = arguments[index];\n    }\n    if (isCurried) {\n      var placeholder = getHolder(wrapper),\n        holdersCount = countHolders(args, placeholder);\n    }\n    if (partials) {\n      args = composeArgs(args, partials, holders, isCurried);\n    }\n    if (partialsRight) {\n      args = composeArgsRight(args, partialsRight, holdersRight, isCurried);\n    }\n    length -= holdersCount;\n    if (isCurried && length < arity) {\n      var newHolders = replaceHolders(args, placeholder);\n      return createRecurry(func, bitmask, createHybrid, wrapper.placeholder, thisArg, args, newHolders, argPos, ary, arity - length);\n    }\n    var thisBinding = isBind ? thisArg : this,\n      fn = isBindKey ? thisBinding[func] : func;\n    length = args.length;\n    if (argPos) {\n      args = reorder(args, argPos);\n    } else if (isFlip && length > 1) {\n      args.reverse();\n    }\n    if (isAry && ary < length) {\n      args.length = ary;\n    }\n    if (this && this !== root && this instanceof wrapper) {\n      fn = Ctor || createCtor(fn);\n    }\n    return fn.apply(thisBinding, args);\n  }\n  return wrapper;\n}\nexport default createHybrid;", "map": {"version": 3, "names": ["compose<PERSON><PERSON>s", "composeArgsRight", "countHolders", "createCtor", "createRecurry", "getHolder", "reorder", "replaceHolders", "root", "WRAP_BIND_FLAG", "WRAP_BIND_KEY_FLAG", "WRAP_CURRY_FLAG", "WRAP_CURRY_RIGHT_FLAG", "WRAP_ARY_FLAG", "WRAP_FLIP_FLAG", "createHybrid", "func", "bitmask", "thisArg", "partials", "holders", "partialsRight", "holdersRight", "argPos", "ary", "arity", "isAry", "isBind", "isBindKey", "is<PERSON><PERSON><PERSON>", "isFlip", "Ctor", "undefined", "wrapper", "length", "arguments", "args", "Array", "index", "placeholder", "holdersCount", "newHolders", "thisBinding", "fn", "reverse", "apply"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_createHybrid.js"], "sourcesContent": ["import composeArgs from './_composeArgs.js';\nimport composeArgsRight from './_composeArgsRight.js';\nimport countHolders from './_countHolders.js';\nimport createCtor from './_createCtor.js';\nimport createRecurry from './_createRecurry.js';\nimport getHolder from './_getHolder.js';\nimport reorder from './_reorder.js';\nimport replaceHolders from './_replaceHolders.js';\nimport root from './_root.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n    WRAP_BIND_KEY_FLAG = 2,\n    WRAP_CURRY_FLAG = 8,\n    WRAP_CURRY_RIGHT_FLAG = 16,\n    WRAP_ARY_FLAG = 128,\n    WRAP_FLIP_FLAG = 512;\n\n/**\n * Creates a function that wraps `func` to invoke it with optional `this`\n * binding of `thisArg`, partial application, and currying.\n *\n * @private\n * @param {Function|string} func The function or method name to wrap.\n * @param {number} bitmask The bitmask flags. See `createWrap` for more details.\n * @param {*} [thisArg] The `this` binding of `func`.\n * @param {Array} [partials] The arguments to prepend to those provided to\n *  the new function.\n * @param {Array} [holders] The `partials` placeholder indexes.\n * @param {Array} [partialsRight] The arguments to append to those provided\n *  to the new function.\n * @param {Array} [holdersRight] The `partialsRight` placeholder indexes.\n * @param {Array} [argPos] The argument positions of the new function.\n * @param {number} [ary] The arity cap of `func`.\n * @param {number} [arity] The arity of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createHybrid(func, bitmask, thisArg, partials, holders, partialsRight, holdersRight, argPos, ary, arity) {\n  var isAry = bitmask & WRAP_ARY_FLAG,\n      isBind = bitmask & WRAP_BIND_FLAG,\n      isBindKey = bitmask & WRAP_BIND_KEY_FLAG,\n      isCurried = bitmask & (WRAP_CURRY_FLAG | WRAP_CURRY_RIGHT_FLAG),\n      isFlip = bitmask & WRAP_FLIP_FLAG,\n      Ctor = isBindKey ? undefined : createCtor(func);\n\n  function wrapper() {\n    var length = arguments.length,\n        args = Array(length),\n        index = length;\n\n    while (index--) {\n      args[index] = arguments[index];\n    }\n    if (isCurried) {\n      var placeholder = getHolder(wrapper),\n          holdersCount = countHolders(args, placeholder);\n    }\n    if (partials) {\n      args = composeArgs(args, partials, holders, isCurried);\n    }\n    if (partialsRight) {\n      args = composeArgsRight(args, partialsRight, holdersRight, isCurried);\n    }\n    length -= holdersCount;\n    if (isCurried && length < arity) {\n      var newHolders = replaceHolders(args, placeholder);\n      return createRecurry(\n        func, bitmask, createHybrid, wrapper.placeholder, thisArg,\n        args, newHolders, argPos, ary, arity - length\n      );\n    }\n    var thisBinding = isBind ? thisArg : this,\n        fn = isBindKey ? thisBinding[func] : func;\n\n    length = args.length;\n    if (argPos) {\n      args = reorder(args, argPos);\n    } else if (isFlip && length > 1) {\n      args.reverse();\n    }\n    if (isAry && ary < length) {\n      args.length = ary;\n    }\n    if (this && this !== root && this instanceof wrapper) {\n      fn = Ctor || createCtor(fn);\n    }\n    return fn.apply(thisBinding, args);\n  }\n  return wrapper;\n}\n\nexport default createHybrid;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,cAAc,GAAG,CAAC;EAClBC,kBAAkB,GAAG,CAAC;EACtBC,eAAe,GAAG,CAAC;EACnBC,qBAAqB,GAAG,EAAE;EAC1BC,aAAa,GAAG,GAAG;EACnBC,cAAc,GAAG,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAChH,IAAIC,KAAK,GAAGT,OAAO,GAAGJ,aAAa;IAC/Bc,MAAM,GAAGV,OAAO,GAAGR,cAAc;IACjCmB,SAAS,GAAGX,OAAO,GAAGP,kBAAkB;IACxCmB,SAAS,GAAGZ,OAAO,IAAIN,eAAe,GAAGC,qBAAqB,CAAC;IAC/DkB,MAAM,GAAGb,OAAO,GAAGH,cAAc;IACjCiB,IAAI,GAAGH,SAAS,GAAGI,SAAS,GAAG7B,UAAU,CAACa,IAAI,CAAC;EAEnD,SAASiB,OAAOA,CAAA,EAAG;IACjB,IAAIC,MAAM,GAAGC,SAAS,CAACD,MAAM;MACzBE,IAAI,GAAGC,KAAK,CAACH,MAAM,CAAC;MACpBI,KAAK,GAAGJ,MAAM;IAElB,OAAOI,KAAK,EAAE,EAAE;MACdF,IAAI,CAACE,KAAK,CAAC,GAAGH,SAAS,CAACG,KAAK,CAAC;IAChC;IACA,IAAIT,SAAS,EAAE;MACb,IAAIU,WAAW,GAAGlC,SAAS,CAAC4B,OAAO,CAAC;QAChCO,YAAY,GAAGtC,YAAY,CAACkC,IAAI,EAAEG,WAAW,CAAC;IACpD;IACA,IAAIpB,QAAQ,EAAE;MACZiB,IAAI,GAAGpC,WAAW,CAACoC,IAAI,EAAEjB,QAAQ,EAAEC,OAAO,EAAES,SAAS,CAAC;IACxD;IACA,IAAIR,aAAa,EAAE;MACjBe,IAAI,GAAGnC,gBAAgB,CAACmC,IAAI,EAAEf,aAAa,EAAEC,YAAY,EAAEO,SAAS,CAAC;IACvE;IACAK,MAAM,IAAIM,YAAY;IACtB,IAAIX,SAAS,IAAIK,MAAM,GAAGT,KAAK,EAAE;MAC/B,IAAIgB,UAAU,GAAGlC,cAAc,CAAC6B,IAAI,EAAEG,WAAW,CAAC;MAClD,OAAOnC,aAAa,CAClBY,IAAI,EAAEC,OAAO,EAAEF,YAAY,EAAEkB,OAAO,CAACM,WAAW,EAAErB,OAAO,EACzDkB,IAAI,EAAEK,UAAU,EAAElB,MAAM,EAAEC,GAAG,EAAEC,KAAK,GAAGS,MACzC,CAAC;IACH;IACA,IAAIQ,WAAW,GAAGf,MAAM,GAAGT,OAAO,GAAG,IAAI;MACrCyB,EAAE,GAAGf,SAAS,GAAGc,WAAW,CAAC1B,IAAI,CAAC,GAAGA,IAAI;IAE7CkB,MAAM,GAAGE,IAAI,CAACF,MAAM;IACpB,IAAIX,MAAM,EAAE;MACVa,IAAI,GAAG9B,OAAO,CAAC8B,IAAI,EAAEb,MAAM,CAAC;IAC9B,CAAC,MAAM,IAAIO,MAAM,IAAII,MAAM,GAAG,CAAC,EAAE;MAC/BE,IAAI,CAACQ,OAAO,CAAC,CAAC;IAChB;IACA,IAAIlB,KAAK,IAAIF,GAAG,GAAGU,MAAM,EAAE;MACzBE,IAAI,CAACF,MAAM,GAAGV,GAAG;IACnB;IACA,IAAI,IAAI,IAAI,IAAI,KAAKhB,IAAI,IAAI,IAAI,YAAYyB,OAAO,EAAE;MACpDU,EAAE,GAAGZ,IAAI,IAAI5B,UAAU,CAACwC,EAAE,CAAC;IAC7B;IACA,OAAOA,EAAE,CAACE,KAAK,CAACH,WAAW,EAAEN,IAAI,CAAC;EACpC;EACA,OAAOH,OAAO;AAChB;AAEA,eAAelB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}