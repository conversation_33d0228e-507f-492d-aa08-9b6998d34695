{"ast": null, "code": "import baseSlice from './_baseSlice.js';\n\n/**\n * Gets all but the last element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.initial([1, 2, 3]);\n * // => [1, 2]\n */\nfunction initial(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseSlice(array, 0, -1) : [];\n}\nexport default initial;", "map": {"version": 3, "names": ["baseSlice", "initial", "array", "length"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/initial.js"], "sourcesContent": ["import baseSlice from './_baseSlice.js';\n\n/**\n * Gets all but the last element of `array`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.initial([1, 2, 3]);\n * // => [1, 2]\n */\nfunction initial(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseSlice(array, 0, -1) : [];\n}\n\nexport default initial;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,IAAIC,MAAM,GAAGD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACC,MAAM;EAC7C,OAAOA,MAAM,GAAGH,SAAS,CAACE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE;AAC9C;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}