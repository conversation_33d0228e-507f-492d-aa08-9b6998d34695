{"ast": null, "code": "import baseFindIndex from './_baseFindIndex.js';\nimport baseIteratee from './_baseIteratee.js';\nimport toInteger from './toInteger.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n  nativeMin = Math.min;\n\n/**\n * This method is like `_.findIndex` except that it iterates over elements\n * of `collection` from right to left.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=array.length-1] The index to search from.\n * @returns {number} Returns the index of the found element, else `-1`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': true },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': false }\n * ];\n *\n * _.findLastIndex(users, function(o) { return o.user == 'pebbles'; });\n * // => 2\n *\n * // The `_.matches` iteratee shorthand.\n * _.findLastIndex(users, { 'user': 'barney', 'active': true });\n * // => 0\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findLastIndex(users, ['active', false]);\n * // => 2\n *\n * // The `_.property` iteratee shorthand.\n * _.findLastIndex(users, 'active');\n * // => 0\n */\nfunction findLastIndex(array, predicate, fromIndex) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return -1;\n  }\n  var index = length - 1;\n  if (fromIndex !== undefined) {\n    index = toInteger(fromIndex);\n    index = fromIndex < 0 ? nativeMax(length + index, 0) : nativeMin(index, length - 1);\n  }\n  return baseFindIndex(array, baseIteratee(predicate, 3), index, true);\n}\nexport default findLastIndex;", "map": {"version": 3, "names": ["baseFindIndex", "baseIteratee", "toInteger", "nativeMax", "Math", "max", "nativeMin", "min", "findLastIndex", "array", "predicate", "fromIndex", "length", "index", "undefined"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/findLastIndex.js"], "sourcesContent": ["import baseFindIndex from './_baseFindIndex.js';\nimport baseIteratee from './_baseIteratee.js';\nimport toInteger from './toInteger.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * This method is like `_.findIndex` except that it iterates over elements\n * of `collection` from right to left.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {Function} [predicate=_.identity] The function invoked per iteration.\n * @param {number} [fromIndex=array.length-1] The index to search from.\n * @returns {number} Returns the index of the found element, else `-1`.\n * @example\n *\n * var users = [\n *   { 'user': 'barney',  'active': true },\n *   { 'user': 'fred',    'active': false },\n *   { 'user': 'pebbles', 'active': false }\n * ];\n *\n * _.findLastIndex(users, function(o) { return o.user == 'pebbles'; });\n * // => 2\n *\n * // The `_.matches` iteratee shorthand.\n * _.findLastIndex(users, { 'user': 'barney', 'active': true });\n * // => 0\n *\n * // The `_.matchesProperty` iteratee shorthand.\n * _.findLastIndex(users, ['active', false]);\n * // => 2\n *\n * // The `_.property` iteratee shorthand.\n * _.findLastIndex(users, 'active');\n * // => 0\n */\nfunction findLastIndex(array, predicate, fromIndex) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return -1;\n  }\n  var index = length - 1;\n  if (fromIndex !== undefined) {\n    index = toInteger(fromIndex);\n    index = fromIndex < 0\n      ? nativeMax(length + index, 0)\n      : nativeMin(index, length - 1);\n  }\n  return baseFindIndex(array, baseIteratee(predicate, 3), index, true);\n}\n\nexport default findLastIndex;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;EACpBC,SAAS,GAAGF,IAAI,CAACG,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAClD,IAAIC,MAAM,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACG,MAAM;EAC7C,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EACA,IAAIC,KAAK,GAAGD,MAAM,GAAG,CAAC;EACtB,IAAID,SAAS,KAAKG,SAAS,EAAE;IAC3BD,KAAK,GAAGX,SAAS,CAACS,SAAS,CAAC;IAC5BE,KAAK,GAAGF,SAAS,GAAG,CAAC,GACjBR,SAAS,CAACS,MAAM,GAAGC,KAAK,EAAE,CAAC,CAAC,GAC5BP,SAAS,CAACO,KAAK,EAAED,MAAM,GAAG,CAAC,CAAC;EAClC;EACA,OAAOZ,aAAa,CAACS,KAAK,EAAER,YAAY,CAACS,SAAS,EAAE,CAAC,CAAC,EAAEG,KAAK,EAAE,IAAI,CAAC;AACtE;AAEA,eAAeL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}