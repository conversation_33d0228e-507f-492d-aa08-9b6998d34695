export interface HotelUnitRental {
  // Step 1: Basic Request Settings
  locationType: string;
  operationType: string;

  // Step 2: Location Information
  cityId: string;
  areaId: string;
  neighborhoodNumber: string;

  // Step 3: Unit Information
  unitNumber: string;
  propertyNumber: string;
  unitType: string;
  unitArea: number | null;
  roomsCount: number | null;
  bathroomsCount: number | null;
  floor: string;
  furnishingStatus: string;
  finishingStatus: string;
  unitView: string;
  unitPosition: string;
  hasParking: boolean;
  hasSwimmingPool: boolean;
  hasElevator: boolean;
  hasClubHouse: boolean;

  // Step 4: Financial Information
  monthlyRentFrom: number | null;
  monthlyRentTo: number | null;
  wantRealEstateAgentOffers: boolean;
  dailyRentFrom: number | null;
  dailyRentTo: number | null;
}
