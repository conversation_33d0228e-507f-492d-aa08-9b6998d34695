{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nlet SignupComponent = class SignupComponent {\n  fb;\n  router;\n  signupForm;\n  currentStep = 1;\n  totalSteps = 3;\n  isLoading = false;\n  constructor(fb, router) {\n    this.fb = fb;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.initForm();\n  }\n  initForm() {\n    this.signupForm = this.fb.group({\n      // Step 1: User Type Selection\n      userType: ['', [Validators.required]],\n      // Step 2: Basic Information\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{11}$/)]],\n      // Step 3: Account Setup\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      agreeToTerms: [false, [Validators.requiredTrue]]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n      return {\n        passwordMismatch: true\n      };\n    }\n    return null;\n  }\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  onUserTypeSelect(userType) {\n    this.signupForm.patchValue({\n      userType\n    });\n    this.nextStep();\n  }\n  onSubmit() {\n    if (this.signupForm.valid) {\n      this.isLoading = true;\n      // Simulate API call\n      setTimeout(() => {\n        console.log('Form submitted:', this.signupForm.value);\n        this.isLoading = false;\n        // Navigate to login or dashboard\n        this.router.navigate(['/auth/signin']);\n      }, 2000);\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.signupForm.controls).forEach(key => {\n      const control = this.signupForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  goToLogin() {\n    this.router.navigate(['/auth/signin']);\n  }\n};\nSignupComponent = __decorate([Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})], SignupComponent);\nexport { SignupComponent };", "map": {"version": 3, "names": ["Component", "Validators", "SignupComponent", "fb", "router", "signupForm", "currentStep", "totalSteps", "isLoading", "constructor", "ngOnInit", "initForm", "group", "userType", "required", "fullName", "<PERSON><PERSON><PERSON><PERSON>", "email", "phone", "pattern", "password", "confirmPassword", "agreeToTerms", "requiredTrue", "validators", "passwordMatchValidator", "form", "get", "value", "setErrors", "passwordMismatch", "nextStep", "previousStep", "onUserTypeSelect", "patchValue", "onSubmit", "valid", "setTimeout", "console", "log", "navigate", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "goToLogin", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\fronLogin\\easydeal-frontend\\src\\app\\pages\\Auth\\SignUp\\signup.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})\nexport class SignupComponent implements OnInit {\n  signupForm: FormGroup;\n  currentStep = 1;\n  totalSteps = 3;\n  isLoading = false;\n\n  constructor(\n    private fb: FormBuilder,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.initForm();\n  }\n\n  initForm() {\n    this.signupForm = this.fb.group({\n      // Step 1: User Type Selection\n      userType: ['', [Validators.required]],\n      \n      // Step 2: Basic Information\n      fullName: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{11}$/)]],\n      \n      // Step 3: Account Setup\n      password: ['', [Validators.required, Validators.minLength(8)]],\n      confirmPassword: ['', [Validators.required]],\n      agreeToTerms: [false, [Validators.requiredTrue]]\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    \n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n      return { passwordMismatch: true };\n    }\n    \n    return null;\n  }\n\n  nextStep() {\n    if (this.currentStep < this.totalSteps) {\n      this.currentStep++;\n    }\n  }\n\n  previousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n\n  onUserTypeSelect(userType: string) {\n    this.signupForm.patchValue({ userType });\n    this.nextStep();\n  }\n\n  onSubmit() {\n    if (this.signupForm.valid) {\n      this.isLoading = true;\n      \n      // Simulate API call\n      setTimeout(() => {\n        console.log('Form submitted:', this.signupForm.value);\n        this.isLoading = false;\n        // Navigate to login or dashboard\n        this.router.navigate(['/auth/signin']);\n      }, 2000);\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  private markFormGroupTouched() {\n    Object.keys(this.signupForm.controls).forEach(key => {\n      const control = this.signupForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  goToLogin() {\n    this.router.navigate(['/auth/signin']);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAAiCC,UAAU,QAAQ,gBAAgB;AAQ5D,IAAMC,eAAe,GAArB,MAAMA,eAAe;EAOhBC,EAAA;EACAC,MAAA;EAPVC,UAAU;EACVC,WAAW,GAAG,CAAC;EACfC,UAAU,GAAG,CAAC;EACdC,SAAS,GAAG,KAAK;EAEjBC,YACUN,EAAe,EACfC,MAAc;IADd,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAA,QAAQA,CAAA;IACN,IAAI,CAACN,UAAU,GAAG,IAAI,CAACF,EAAE,CAACS,KAAK,CAAC;MAC9B;MACAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACZ,UAAU,CAACa,QAAQ,CAAC,CAAC;MAErC;MACAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACd,UAAU,CAACa,QAAQ,EAAEb,UAAU,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAChB,UAAU,CAACa,QAAQ,EAAEb,UAAU,CAACgB,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjB,UAAU,CAACa,QAAQ,EAAEb,UAAU,CAACkB,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAErE;MACAC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnB,UAAU,CAACa,QAAQ,EAAEb,UAAU,CAACe,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DK,eAAe,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACa,QAAQ,CAAC,CAAC;MAC5CQ,YAAY,EAAE,CAAC,KAAK,EAAE,CAACrB,UAAU,CAACsB,YAAY,CAAC;KAChD,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAA,sBAAsBA,CAACC,IAAe;IACpC,MAAMN,QAAQ,GAAGM,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMN,eAAe,GAAGK,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAIP,QAAQ,IAAIC,eAAe,IAAID,QAAQ,CAACQ,KAAK,KAAKP,eAAe,CAACO,KAAK,EAAE;MAC3EP,eAAe,CAACQ,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;MACrD,OAAO;QAAEA,gBAAgB,EAAE;MAAI,CAAE;IACnC;IAEA,OAAO,IAAI;EACb;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzB,WAAW,GAAG,IAAI,CAACC,UAAU,EAAE;MACtC,IAAI,CAACD,WAAW,EAAE;IACpB;EACF;EAEA0B,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC1B,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;EACF;EAEA2B,gBAAgBA,CAACpB,QAAgB;IAC/B,IAAI,CAACR,UAAU,CAAC6B,UAAU,CAAC;MAAErB;IAAQ,CAAE,CAAC;IACxC,IAAI,CAACkB,QAAQ,EAAE;EACjB;EAEAI,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC9B,UAAU,CAAC+B,KAAK,EAAE;MACzB,IAAI,CAAC5B,SAAS,GAAG,IAAI;MAErB;MACA6B,UAAU,CAAC,MAAK;QACdC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAClC,UAAU,CAACuB,KAAK,CAAC;QACrD,IAAI,CAACpB,SAAS,GAAG,KAAK;QACtB;QACA,IAAI,CAACJ,MAAM,CAACoC,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACxC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL,IAAI,CAACC,oBAAoB,EAAE;IAC7B;EACF;EAEQA,oBAAoBA,CAAA;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtC,UAAU,CAACuC,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMC,OAAO,GAAG,IAAI,CAAC1C,UAAU,CAACsB,GAAG,CAACmB,GAAG,CAAC;MACxCC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,SAASA,CAAA;IACP,IAAI,CAAC7C,MAAM,CAACoC,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;EACxC;CACD;AAvFYtC,eAAe,GAAAgD,UAAA,EAL3BlD,SAAS,CAAC;EACTmD,QAAQ,EAAE,YAAY;EACtBC,WAAW,EAAE,yBAAyB;EACtCC,SAAS,EAAE,CAAC,yBAAyB;CACtC,CAAC,C,EACWnD,eAAe,CAuF3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}