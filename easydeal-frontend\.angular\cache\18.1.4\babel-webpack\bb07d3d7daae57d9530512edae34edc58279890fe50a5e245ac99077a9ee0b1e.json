{"ast": null, "code": "import baseSortedIndexBy from './_baseSortedIndexBy.js';\nimport identity from './identity.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295,\n  HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH >>> 1;\n\n/**\n * The base implementation of `_.sortedIndex` and `_.sortedLastIndex` which\n * performs a binary search of `array` to determine the index at which `value`\n * should be inserted into `array` in order to maintain its sort order.\n *\n * @private\n * @param {Array} array The sorted array to inspect.\n * @param {*} value The value to evaluate.\n * @param {boolean} [retHighest] Specify returning the highest qualified index.\n * @returns {number} Returns the index at which `value` should be inserted\n *  into `array`.\n */\nfunction baseSortedIndex(array, value, retHighest) {\n  var low = 0,\n    high = array == null ? low : array.length;\n  if (typeof value == 'number' && value === value && high <= HALF_MAX_ARRAY_LENGTH) {\n    while (low < high) {\n      var mid = low + high >>> 1,\n        computed = array[mid];\n      if (computed !== null && !isSymbol(computed) && (retHighest ? computed <= value : computed < value)) {\n        low = mid + 1;\n      } else {\n        high = mid;\n      }\n    }\n    return high;\n  }\n  return baseSortedIndexBy(array, value, identity, retHighest);\n}\nexport default baseSortedIndex;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}