{"ast": null, "code": "import arrayMap from './_arrayMap.js';\n\n/**\n * The base implementation of `_.toPairs` and `_.toPairsIn` which creates an array\n * of key-value pairs for `object` corresponding to the property names of `props`.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array} props The property names to get values for.\n * @returns {Object} Returns the key-value pairs.\n */\nfunction baseToPairs(object, props) {\n  return arrayMap(props, function (key) {\n    return [key, object[key]];\n  });\n}\nexport default baseToPairs;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}