:host {
  width: 100%;
  height: 100vh;
  
  @media (min-width: 992px) {
    .signup-form {
      width: 100%;
      max-width: 550px;

      .mat-form-field {
        width: 100%;
      }
    }
  }
}

// Background styling
#kt_app_root {
  min-height: 100vh;
  background-image: url('assets/media/login/EaseDealPage.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

// Card styling
.card {
  box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
  border: 0;
  
  .card-body {
    padding: 3rem 2.5rem;
    
    @media (max-width: 991.98px) {
      padding: 2rem 1.5rem;
    }
  }
}

// Form styling
.form-control {
  border: 1px solid #e4e6ef;
  border-radius: 0.475rem;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  
  &:focus {
    border-color: #009ef7;
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
  }
  
  &.is-valid {
    border-color: #50cd89;
    
    &:focus {
      border-color: #50cd89;
      box-shadow: 0 0 0 0.2rem rgba(80, 205, 137, 0.25);
    }
  }
  
  &.is-invalid {
    border-color: #f1416c;
    
    &:focus {
      border-color: #f1416c;
      box-shadow: 0 0 0 0.2rem rgba(241, 65, 108, 0.25);
    }
  }
}

.form-select {
  border: 1px solid #e4e6ef;
  border-radius: 0.475rem;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  
  &:focus {
    border-color: #009ef7;
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
  }
  
  &.is-valid {
    border-color: #50cd89;
    
    &:focus {
      border-color: #50cd89;
      box-shadow: 0 0 0 0.2rem rgba(80, 205, 137, 0.25);
    }
  }
  
  &.is-invalid {
    border-color: #f1416c;
    
    &:focus {
      border-color: #f1416c;
      box-shadow: 0 0 0 0.2rem rgba(241, 65, 108, 0.25);
    }
  }
}

// Button styling
.btn-primary {
  background-color: #009ef7;
  border-color: #009ef7;
  color: #ffffff;
  border-radius: 0.475rem;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  
  &:hover {
    background-color: #0095e8;
    border-color: #0095e8;
  }
  
  &:focus {
    background-color: #0095e8;
    border-color: #0095e8;
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
  }
  
  &:disabled {
    background-color: #e4e6ef;
    border-color: #e4e6ef;
    color: #a1a5b7;
  }
}

// Error message styling
.fv-plugins-message-container {
  margin-top: 0.5rem;
  
  .fv-help-block {
    color: #f1416c;
    font-size: 0.875rem;
    font-weight: 500;
  }
}

// Alert styling
.alert-danger {
  background-color: #fff5f8;
  border-color: #f7d6e6;
  color: #f1416c;
  border-radius: 0.475rem;
  
  .alert-text {
    font-size: 0.875rem;
  }
}

// Checkbox styling
.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-top: 0.125rem;
  border: 1px solid #e4e6ef;
  border-radius: 0.25rem;
  
  &:checked {
    background-color: #009ef7;
    border-color: #009ef7;
  }
  
  &:focus {
    border-color: #009ef7;
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
  }
}

// Logo and title styling
.h-60px {
  height: 60px;
}

// Responsive adjustments
@media (max-width: 991.98px) {
  .w-lg-50 {
    width: 100% !important;
  }
  
  .pt-lg-0 {
    padding-top: 0 !important;
  }
  
  .flex-lg-row {
    flex-direction: column !important;
  }
  
  .flex-lg-start {
    align-items: center !important;
  }
}

// RTL support
[dir="rtl"] {
  .form-select {
    background-position: left 0.75rem center;
  }
  
  .form-check-label {
    margin-right: 0.5rem;
    margin-left: 0;
  }
}
