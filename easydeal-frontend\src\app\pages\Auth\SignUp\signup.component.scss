.signup-container {
  display: flex;
  min-height: 100vh;
  direction: rtl;
  font-family: 'Cairo', sans-serif;
}

.background-section {
  flex: 1;
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
  background-image: url('../../../../assets/media/login/EaseDealPage.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 2rem;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.8) 0%, rgba(59, 130, 246, 0.6) 50%, rgba(30, 64, 175, 0.8) 100%);
  }
}

.logo-container {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  gap: 1rem;

  &::before {
    content: '🏠';
    font-size: 3rem;
    color: #10b981;
    background: white;
    border-radius: 12px;
    padding: 0.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .logo-text {
    color: white;
    font-family: 'Arial', sans-serif;
    font-weight: 900;
    font-size: 3rem;
    line-height: 0.9;
    text-align: left;

    .logo-main {
      display: block;
      letter-spacing: 2px;
    }
  }
}

.form-section {
  flex: 0 0 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: #f8fafc;
}

.form-card {
  background: white;
  border-radius: 20px;
  padding: 3rem 2.5rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  text-align: center;
}

.step-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.form-subtitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 1rem;
}

.form-description {
  font-size: 0.95rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.user-type-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
}

.user-type-btn {
  flex: 1;
  min-width: 100px;
  padding: 1rem 0.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  color: #64748b;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;

  i {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
  }

  &:hover {
    border-color: #3b82f6;
    color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  }

  &.selected {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
  }
}

.signup-form {
  text-align: right;
}

.form-group {
  margin-bottom: 1.5rem;
  text-align: right;
}

.form-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.input-group {
  position: relative;
}

.form-control {
  width: 100%;
  padding: 0.875rem 1rem;
  padding-left: 3rem;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f9fafb;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
    font-size: 0.9rem;
  }
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 1.1rem;
}

.form-footer {
  margin-top: 2rem;
}

.btn {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.btn-primary {
  background: #3b82f6;
  color: white;

  &:hover:not(:disabled) {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
  }

  &.disabled,
  &:disabled {
    background: #d1d5db;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  &.loading {
    background: #6b7280;
    cursor: wait;
  }
}

.login-link {
  text-align: center;
  font-size: 0.9rem;
  color: #6b7280;

  .link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .signup-container {
    flex-direction: column;
  }

  .background-section {
    flex: 0 0 200px;
    padding: 1rem;
  }

  .form-section {
    flex: 1;
    padding: 1rem;
  }

  .form-card {
    padding: 2rem 1.5rem;
  }

  .user-type-buttons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .user-type-btn {
    min-width: auto;
  }
}
