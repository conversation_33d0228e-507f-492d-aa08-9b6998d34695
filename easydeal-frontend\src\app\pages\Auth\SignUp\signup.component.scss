// Background and layout styles
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow-x: hidden;
}

#kt_app_root {
  min-height: 100vh;
  background: linear-gradient(135deg, rgba(13, 110, 253, 0.8), rgba(25, 135, 84, 0.6)), 
              url('assets/media/login/EaseDealPage.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

// Card styling
.card {
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// Form controls
.form-control {
  border: 2px solid rgba(108, 117, 125, 0.2);
  border-radius: 10px;
  padding: 15px 20px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
  
  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    background: rgba(255, 255, 255, 1);
  }
  
  &::placeholder {
    color: #6c757d;
    opacity: 0.8;
  }
}

// User type selection buttons
.btn-outline-primary {
  border: 2px solid #0d6efd;
  color: #0d6efd;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  border-radius: 15px;
  
  &:hover {
    background: rgba(13, 110, 253, 0.1);
    border-color: #0d6efd;
    color: #0d6efd;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
  }
  
  &.btn-primary {
    background: #0d6efd;
    border-color: #0d6efd;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.4);
  }
  
  i {
    transition: all 0.3s ease;
  }
  
  &:hover i,
  &.btn-primary i {
    transform: scale(1.1);
  }
}

// Primary button styling
.btn-primary {
  background: linear-gradient(135deg, #0d6efd, #0b5ed7);
  border: none;
  border-radius: 10px;
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #0b5ed7, #0a58ca);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.4);
  }
  
  &:disabled {
    background: #6c757d;
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Checkbox styling
.form-check-input {
  border-radius: 5px;
  border: 2px solid #dee2e6;
  
  &:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
}

// Error message styling
.fv-plugins-message-container {
  margin-top: 8px;
  
  .fv-help-block {
    color: #dc3545;
    font-size: 14px;
    font-weight: 500;
    display: block;
    margin-top: 5px;
  }
}

// Link styling
.link-primary {
  color: #0d6efd;
  text-decoration: none;
  font-weight: 600;
  
  &:hover {
    color: #0b5ed7;
    text-decoration: underline;
  }
}

// Logo styling
.h-60px {
  height: 60px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

// Text shadow for better readability
.text-white {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

// Responsive adjustments
@media (max-width: 991.98px) {
  .card {
    margin: 20px;
  }
  
  .h-60px {
    height: 50px;
  }
  
  .btn-outline-primary {
    height: 70px !important;
    font-size: 14px;
    
    i {
      font-size: 1.5rem !important;
    }
  }
}

@media (max-width: 575.98px) {
  .card-body {
    padding: 20px !important;
  }
  
  .btn-outline-primary {
    height: 60px !important;
    font-size: 12px;
    
    i {
      font-size: 1.2rem !important;
      margin-bottom: 5px !important;
    }
  }
  
  .form-control {
    padding: 12px 15px;
    font-size: 14px;
  }
}

// Animation for form validation
.form-control.is-invalid {
  border-color: #dc3545;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

// Loading spinner
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}
