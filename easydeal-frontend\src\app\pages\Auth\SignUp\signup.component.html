<!-- Main Container -->
<div class="signup-container">
  <!-- Background Image -->
  <div class="background-section">
    <!-- Logo -->
    <div class="logo-container">
      <div class="logo-text">
        <div class="logo-main">EASY</div>
        <div class="logo-main">DEAL</div>
      </div>
    </div>
  </div>

  <!-- Form Section -->
  <div class="form-section">
    <div class="form-card">
      <!-- Step 1: User Type Selection -->
      <div *ngIf="currentStep === 1" class="step-content">
        <h2 class="form-title">مرحباً بك في إيزي ديل</h2>
        <p class="form-subtitle">حدد طبيعتك كمستخدم</p>
        <p class="form-description">
          نقدم لك الحساب الذي يناسب طبيعة عملك للتسجيل في الموقع وتحقيق أهدافك بسهولة
        </p>

        <div class="user-type-buttons">
          <button type="button" class="user-type-btn" (click)="onUserTypeSelect('developer')">
            <i class="fas fa-building"></i>
            مطور
          </button>

          <button type="button" class="user-type-btn" (click)="onUserTypeSelect('broker')">
            <i class="fas fa-handshake"></i>
            وسيط
          </button>

          <button type="button" class="user-type-btn" (click)="onUserTypeSelect('client')">
            <i class="fas fa-user"></i>
            عميل
          </button>
        </div>

        <div class="form-footer">
          <button type="button" class="btn btn-primary btn-next disabled">
            اختر
          </button>

          <div class="login-link">
            <span>لديك حساب؟ </span>
            <a (click)="goToLogin()" class="link">تسجيل الدخول من هنا</a>
          </div>
        </div>
      </div>

      <!-- Step 2: Basic Information -->
      <div *ngIf="currentStep === 2" class="step-content">
        <h2 class="form-title">مرحباً بك عميل إيزي ديل</h2>
        <p class="form-subtitle">حدد طبيعتك كمستخدم</p>
        <p class="form-description">
          نقدم لك الحساب الذي يناسب طبيعة عملك للتسجيل في الموقع وتحقيق أهدافك بسهولة
        </p>

        <form [formGroup]="signupForm" class="signup-form">
          <div class="user-type-buttons">
            <button type="button" class="user-type-btn"
              [class.selected]="signupForm.get('userType')?.value === 'developer'">
              <i class="fas fa-building"></i>
              مطور
            </button>

            <button type="button" class="user-type-btn"
              [class.selected]="signupForm.get('userType')?.value === 'broker'">
              <i class="fas fa-handshake"></i>
              وسيط
            </button>

            <button type="button" class="user-type-btn"
              [class.selected]="signupForm.get('userType')?.value === 'client'">
              <i class="fas fa-user"></i>
              عميل
            </button>
          </div>

          <div class="form-footer">
            <button type="button" class="btn btn-primary btn-next" (click)="nextStep()">
              اختر
            </button>

            <div class="login-link">
              <span>لديك حساب؟ </span>
              <a (click)="goToLogin()" class="link">تسجيل الدخول من هنا</a>
            </div>
          </div>
        </form>
      </div>

      <!-- Step 3: Account Information -->
      <div *ngIf="currentStep === 3" class="step-content">
        <h2 class="form-title">ممكن نتعرف بيك أكتر؟</h2>
        <p class="form-subtitle">ادخل بياناتك الأساسية</p>

        <form [formGroup]="signupForm" (ngSubmit)="onSubmit()" class="signup-form">
          <div class="form-group">
            <label class="form-label">الاسم كاملاً</label>
            <div class="input-group">
              <input type="text" class="form-control" formControlName="fullName"
                placeholder="مثال: محمد أحمد محمود الحديد">
              <span class="input-icon">
                <i class="fas fa-user"></i>
              </span>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">البريد الإلكتروني</label>
            <div class="input-group">
              <input type="email" class="form-control" formControlName="email"
                placeholder="ادخل البريد الإلكتروني أو رقم الموبايل">
              <span class="input-icon">
                <i class="fas fa-envelope"></i>
              </span>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">رقم الهاتف</label>
            <input type="tel" class="form-control" formControlName="phone" placeholder="+20 1234567890">
          </div>

          <div class="form-footer">
            <button type="submit" class="btn btn-primary btn-submit" [class.loading]="isLoading" [disabled]="isLoading">
              <span *ngIf="!isLoading">إرسال رمز التحقق</span>
              <span *ngIf="isLoading">جاري الإرسال...</span>
            </button>

            <div class="login-link">
              <span>لديك حساب؟ </span>
              <a (click)="goToLogin()" class="link">تسجيل الدخول من هنا</a>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
