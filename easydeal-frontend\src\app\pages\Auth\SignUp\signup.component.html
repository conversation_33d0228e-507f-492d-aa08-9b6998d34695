<!--begin::Root-->
<div class="d-flex flex-column flex-root" id="kt_app_root">
  <!--begin::Page bg image-->
  <style>
    body {
      background-image: url('assets/media/login/EaseDealPage.png');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      background-attachment: fixed;
    }
  </style>
  <!--end::Page bg image-->

  <!--begin::Authentication - Sign-up -->
  <div class="d-flex flex-column flex-column-fluid flex-lg-row">
    <!--begin::Aside-->
    <div class="d-flex flex-center w-lg-50 pt-15 pt-lg-0 px-10">
      <!--begin::Aside-->
      <div class="d-flex flex-center flex-lg-start flex-column">
        <!--begin::Logo-->
        <a href="#" class="mb-7">
          <img alt="Logo" src="assets/media/easydeallogos/Easy Deal.png" class="h-60px" />
        </a>
        <!--end::Logo-->

        <!--begin::Title-->
        <h2 class="text-white fw-normal m-0" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
          مرحباً بك في إيزي ديل
        </h2>
        <!--end::Title-->
      </div>
      <!--begin::Aside-->
    </div>
    <!--begin::Aside-->

    <!--begin::Body-->
    <div class="d-flex flex-center w-lg-50 p-10">
      <!--begin::Card-->
      <div class="card rounded-3 w-md-550px" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
        <!--begin::Card body-->
        <div class="card-body p-10 p-lg-20">
          <!--begin::Form-->
          <form class="form w-100" [formGroup]="signupForm" novalidate="novalidate" (ngSubmit)="submit()">
            <!--begin::Heading-->
            <div class="text-center mb-11">
              <!--begin::Title-->
              <h1 class="text-gray-900 fw-bolder mb-3">حدد طبيعتك كمستخدم</h1>
              <!--end::Title-->

              <!--begin::Subtitle-->
              <div class="text-gray-500 fw-semibold fs-6">
                اختر نوع الحساب الذي تريد التسجيل به لتتمكن من الوصول إلى جميع
                <br />
                خدمات و مميزات موقع إيزي ديل
              </div>
              <!--end::Subtitle-->
            </div>
            <!--end::Heading-->

            <!--begin::User Type Selection-->
            <div class="row mb-8">
              <div class="col-4">
                <div class="text-center">
                  <button type="button" 
                          class="btn btn-outline-primary w-100 h-80px d-flex flex-column justify-content-center"
                          [class.btn-primary]="selectedUserType === 'developer'"
                          [class.text-white]="selectedUserType === 'developer'"
                          (click)="selectUserType('developer')">
                    <i class="fas fa-building fs-2x mb-2"></i>
                    <span>مطور</span>
                  </button>
                </div>
              </div>
              <div class="col-4">
                <div class="text-center">
                  <button type="button" 
                          class="btn btn-outline-primary w-100 h-80px d-flex flex-column justify-content-center"
                          [class.btn-primary]="selectedUserType === 'broker'"
                          [class.text-white]="selectedUserType === 'broker'"
                          (click)="selectUserType('broker')">
                    <i class="fas fa-handshake fs-2x mb-2"></i>
                    <span>وسيط</span>
                  </button>
                </div>
              </div>
              <div class="col-4">
                <div class="text-center">
                  <button type="button" 
                          class="btn btn-outline-primary w-100 h-80px d-flex flex-column justify-content-center"
                          [class.btn-primary]="selectedUserType === 'client'"
                          [class.text-white]="selectedUserType === 'client'"
                          (click)="selectUserType('client')">
                    <i class="fas fa-user fs-2x mb-2"></i>
                    <span>عميل</span>
                  </button>
                </div>
              </div>
            </div>
            <!--end::User Type Selection-->

            <!--begin::Input group=-->
            <div class="fv-row mb-8">
              <!--begin::Name-->
              <input type="text" 
                     placeholder="الاسم الكامل" 
                     name="fullname" 
                     autocomplete="off"
                     class="form-control bg-transparent" 
                     formControlName="fullname" />
              <!--end::Name-->
              
              <!--begin::Error-->
              <div *ngIf="f['fullname'].invalid && (f['fullname'].dirty || f['fullname'].touched)" 
                   class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span *ngIf="f['fullname'].errors?.['required']">الاسم الكامل مطلوب</span>
                  <span *ngIf="f['fullname'].errors?.['minlength']">الاسم يجب أن يكون 3 أحرف على الأقل</span>
                </div>
              </div>
              <!--end::Error-->
            </div>
            <!--end::Input group=-->

            <!--begin::Input group=-->
            <div class="fv-row mb-8">
              <!--begin::Email-->
              <input type="email" 
                     placeholder="البريد الإلكتروني" 
                     name="email" 
                     autocomplete="off"
                     class="form-control bg-transparent" 
                     formControlName="email" />
              <!--end::Email-->
              
              <!--begin::Error-->
              <div *ngIf="f['email'].invalid && (f['email'].dirty || f['email'].touched)" 
                   class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span *ngIf="f['email'].errors?.['required']">البريد الإلكتروني مطلوب</span>
                  <span *ngIf="f['email'].errors?.['email']">البريد الإلكتروني غير صحيح</span>
                </div>
              </div>
              <!--end::Error-->
            </div>
            <!--end::Input group=-->

            <!--begin::Input group-->
            <div class="fv-row mb-8" data-kt-password-meter="true">
              <!--begin::Wrapper-->
              <div class="mb-1">
                <!--begin::Input wrapper-->
                <div class="position-relative mb-3">
                  <input class="form-control bg-transparent" 
                         type="password" 
                         placeholder="كلمة المرور" 
                         name="password"
                         autocomplete="off" 
                         formControlName="password" />
                </div>
                <!--end::Input wrapper-->
              </div>
              <!--end::Wrapper-->
              
              <!--begin::Error-->
              <div *ngIf="f['password'].invalid && (f['password'].dirty || f['password'].touched)" 
                   class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span *ngIf="f['password'].errors?.['required']">كلمة المرور مطلوبة</span>
                  <span *ngIf="f['password'].errors?.['minlength']">كلمة المرور يجب أن تكون 6 أحرف على الأقل</span>
                </div>
              </div>
              <!--end::Error-->
            </div>
            <!--end::Input group=-->

            <!--begin::Input group=-->
            <div class="fv-row mb-8">
              <!--begin::Repeat Password-->
              <input type="password" 
                     placeholder="تأكيد كلمة المرور" 
                     name="confirm-password" 
                     autocomplete="off"
                     class="form-control bg-transparent" 
                     formControlName="confirmPassword" />
              <!--end::Repeat Password-->
              
              <!--begin::Error-->
              <div *ngIf="f['confirmPassword'].invalid && (f['confirmPassword'].dirty || f['confirmPassword'].touched)" 
                   class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span *ngIf="f['confirmPassword'].errors?.['required']">تأكيد كلمة المرور مطلوب</span>
                  <span *ngIf="f['confirmPassword'].errors?.['passwordMismatch']">كلمة المرور غير متطابقة</span>
                </div>
              </div>
              <!--end::Error-->
            </div>
            <!--end::Input group=-->

            <!--begin::Accept-->
            <div class="fv-row mb-8">
              <label class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" name="toc" formControlName="agree" />
                <span class="form-check-label fw-semibold text-gray-700 fs-base ms-1">
                  أوافق على
                  <a href="#" class="ms-1 link-primary">الشروط والأحكام</a>.
                </span>
              </label>
              
              <!--begin::Error-->
              <div *ngIf="f['agree'].invalid && (f['agree'].dirty || f['agree'].touched)" 
                   class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span *ngIf="f['agree'].errors?.['required']">يجب الموافقة على الشروط والأحكام</span>
                </div>
              </div>
              <!--end::Error-->
            </div>
            <!--end::Accept-->

            <!--begin::Submit button-->
            <div class="d-grid mb-10">
              <button type="submit" 
                      id="kt_sign_up_submit" 
                      class="btn btn-primary"
                      [disabled]="signupForm.invalid">
                <!--begin::Indicator label-->
                <span class="indicator-label" *ngIf="!(isLoading$ | async)">أخر</span>
                <!--end::Indicator label-->

                <!--begin::Indicator progress-->
                <span class="indicator-progress" *ngIf="isLoading$ | async">
                  يرجى الانتظار...
                  <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
                <!--end::Indicator progress-->
              </button>
            </div>
            <!--end::Submit button-->

            <!--begin::Sign up-->
            <div class="text-gray-500 text-center fw-semibold fs-6">
              لديك حساب بالفعل؟
              <a href="/auth/login" class="link-primary fw-semibold">تسجيل الدخول</a>
            </div>
            <!--end::Sign up-->
          </form>
          <!--end::Form-->
        </div>
        <!--end::Card body-->
      </div>
      <!--end::Card-->
    </div>
    <!--end::Body-->
  </div>
  <!--end::Authentication - Sign-up-->
</div>
<!--end::Root-->
