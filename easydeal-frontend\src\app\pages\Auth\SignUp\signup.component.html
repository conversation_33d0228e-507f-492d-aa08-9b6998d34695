<!--begin::Root-->
<div class="d-flex flex-column flex-root" id="kt_app_root">
  <!--begin::Page bg image-->
  <style>
    body {
      background-image: url('assets/media/login/EaseDealPage.png');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      background-attachment: fixed;
    }
  </style>
  <!--end::Page bg image-->

  <!--begin::Authentication - Sign-up -->
  <div class="d-flex flex-column flex-column-fluid flex-lg-row">
    <!--begin::Aside-->
    <div class="d-flex flex-center w-lg-50 pt-15 pt-lg-0 px-10">
      <!--begin::Aside-->
      <div class="d-flex flex-center flex-lg-start flex-column">
        <!--begin::Logo-->
        <a href="#" class="mb-7">
          <img alt="Logo" src="assets/media/easydeallogos/Easy Deal.png" class="h-60px" />
        </a>
        <!--end::Logo-->

        <!--begin::Title-->
        <h2 class="text-white fw-normal m-0" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
          مرحباً بك في إيزي ديل
        </h2>
        <!--end::Title-->
      </div>
      <!--begin::Aside-->
    </div>
    <!--begin::Aside-->

    <!--begin::Body-->
    <div class="d-flex flex-center w-lg-50 p-10">
      <!--begin::Card-->
      <div class="card rounded-3 w-md-550px"
        style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
        <!--begin::Card body-->
        <div class="card-body p-10 p-lg-20">
          <!--begin::Form-->
          <form class="form w-100" [formGroup]="signupForm" novalidate="novalidate" (ngSubmit)="submit()">
            <!--begin::Heading-->
            <div class="text-center mb-11">
              <!--begin::Title-->
              <h1 class="text-gray-900 fw-bolder mb-3">حدد طبيعتك كمستخدم</h1>
              <!--end::Title-->

              <!--begin::Subtitle-->
              <div class="text-gray-500 fw-semibold fs-6">
                اختر نوع الحساب الذي تريد التسجيل به لتتمكن من الوصول إلى جميع
                <br />
                خدمات و مميزات موقع إيزي ديل
              </div>
              <!--end::Subtitle-->
            </div>
            <!--end::Heading-->

            <!--begin::User Type Selection-->
            <div class="fv-row mb-8">
              <label class="form-label fw-bolder text-gray-900 fs-6">نوع المستخدم</label>
              <select class="form-select bg-transparent" formControlName="userType" [ngClass]="{
                'is-invalid': signupForm.controls['userType'].invalid,
                'is-valid': signupForm.controls['userType'].valid
              }">
                <option value="">اختر نوع المستخدم</option>
                <option value="broker">وسيط عقاري</option>
                <option value="developer">مطور عقاري</option>
                <option value="client">عميل</option>
              </select>
              <div *ngIf="signupForm.controls['userType'].hasError('required') && (signupForm.controls['userType'].dirty || signupForm.controls['userType'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">نوع المستخدم مطلوب</span>
                </div>
              </div>
            </div>
            <!--end::User Type Selection-->

            <!--begin::Form group-->
            <div class="fv-row mb-8">
              <label class="form-label fw-bolder text-gray-900 fs-6">الاسم الكامل</label>
              <input class="form-control bg-transparent" type="text" name="fullname" formControlName="fullname"
                placeholder="الاسم الكامل" autocomplete="off" [ngClass]="{
                  'is-invalid': signupForm.controls['fullname'].invalid,
                  'is-valid': signupForm.controls['fullname'].valid
                }" />
              <div *ngIf="signupForm.controls['fullname'].hasError('required') && (signupForm.controls['fullname'].dirty || signupForm.controls['fullname'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">الاسم الكامل مطلوب</span>
                </div>
              </div>
              <div *ngIf="signupForm.controls['fullname'].hasError('minlength') && (signupForm.controls['fullname'].dirty || signupForm.controls['fullname'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">الاسم يجب أن يكون 3 أحرف على الأقل</span>
                </div>
              </div>
            </div>
            <!--end::Form group-->

            <!--begin::Form group-->
            <div class="fv-row mb-8">
              <label class="form-label fw-bolder text-gray-900 fs-6">البريد الإلكتروني</label>
              <input class="form-control bg-transparent" type="email" placeholder="البريد الإلكتروني" name="email" formControlName="email"
                autocomplete="off" [ngClass]="{
                  'is-invalid': signupForm.controls['email'].invalid,
                  'is-valid': signupForm.controls['email'].valid
                }" />
              <div *ngIf="signupForm.controls['email'].hasError('required') && (signupForm.controls['email'].dirty || signupForm.controls['email'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">البريد الإلكتروني مطلوب</span>
                </div>
              </div>
              <div *ngIf="signupForm.controls['email'].hasError('email') && (signupForm.controls['email'].dirty || signupForm.controls['email'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">البريد الإلكتروني غير صحيح</span>
                </div>
              </div>
            </div>
            <!--end::Form group-->

            <!--begin::Form group-->
            <div class="fv-row mb-8">
              <label class="form-label fw-bolder text-gray-900 fs-6">رقم الهاتف</label>
              <input class="form-control bg-transparent" type="tel" placeholder="رقم الهاتف" name="phone" formControlName="phone"
                autocomplete="off" [ngClass]="{
                  'is-invalid': signupForm.controls['phone'].invalid,
                  'is-valid': signupForm.controls['phone'].valid
                }" />
              <div *ngIf="signupForm.controls['phone'].hasError('required') && (signupForm.controls['phone'].dirty || signupForm.controls['phone'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">رقم الهاتف مطلوب</span>
                </div>
              </div>
            </div>
            <!--end::Form group-->

            <!--begin::Form group-->
            <div class="fv-row mb-8">
              <label class="form-label fw-bolder text-gray-900 fs-6">كلمة المرور</label>
              <input class="form-control bg-transparent" type="password" placeholder="كلمة المرور" name="password"
                formControlName="password" autocomplete="off" [ngClass]="{
                  'is-invalid': signupForm.controls['password'].invalid,
                  'is-valid': signupForm.controls['password'].valid
                }" />
              <div *ngIf="signupForm.controls['password'].hasError('required') && (signupForm.controls['password'].dirty || signupForm.controls['password'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">كلمة المرور مطلوبة</span>
                </div>
              </div>
              <div *ngIf="signupForm.controls['password'].hasError('minlength') && (signupForm.controls['password'].dirty || signupForm.controls['password'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">كلمة المرور يجب أن تكون 6 أحرف على الأقل</span>
                </div>
              </div>
            </div>
            <!--end::Form group-->

            <!--begin::Form group-->
            <div class="fv-row mb-8">
              <label class="form-label fw-bolder text-gray-900 fs-6">تأكيد كلمة المرور</label>
              <input class="form-control bg-transparent" type="password" placeholder="تأكيد كلمة المرور" name="confirmPassword"
                autocomplete="off" formControlName="confirmPassword" [ngClass]="{
                  'is-invalid': signupForm.controls['confirmPassword'].invalid,
                  'is-valid': signupForm.controls['confirmPassword'].valid
                }" />
              <div *ngIf="signupForm.controls['confirmPassword'].hasError('required') && (signupForm.controls['confirmPassword'].dirty || signupForm.controls['confirmPassword'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">تأكيد كلمة المرور مطلوب</span>
                </div>
              </div>
            </div>
            <!--end::Form group-->

            <!--begin::Form group-->
            <div class="fv-row mb-8">
              <label class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" name="agree" formControlName="agree" />
                <span class="form-check-label fw-semibold text-gray-700 fs-base ms-1">
                  أوافق على
                  <a href="#" class="ms-1 link-primary">الشروط والأحكام</a>
                </span>
              </label>
              <div *ngIf="signupForm.controls['agree'].hasError('required') && (signupForm.controls['agree'].dirty || signupForm.controls['agree'].touched)" class="fv-plugins-message-container">
                <div class="fv-help-block">
                  <span role="alert">يجب الموافقة على الشروط والأحكام</span>
                </div>
              </div>
            </div>
            <!--end::Form group-->

            <!--begin::Error-->
            <div *ngIf="hasError" class="mb-lg-15 alert alert-danger">
              <div class="alert-text font-weight-bold">
                حدث خطأ أثناء التسجيل. يرجى المحاولة مرة أخرى.
              </div>
            </div>
            <!--end::Error-->

            <!--begin::Submit button-->
            <div class="d-grid mb-10">
              <button type="submit" id="kt_sign_up_submit" class="btn btn-primary" [disabled]="signupForm.invalid">
                <!--begin::Indicator label-->
                <span class="indicator-label" *ngIf="!(isLoading$ | async)">تسجيل</span>
                <!--end::Indicator label-->

                <!--begin::Indicator progress-->
                <span class="indicator-progress" *ngIf="isLoading$ | async">
                  يرجى الانتظار...
                  <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
                <!--end::Indicator progress-->
              </button>
            </div>
            <!--end::Submit button-->

            <!--begin::Sign up-->
            <div class="text-gray-500 text-center fw-semibold fs-6">
              لديك حساب بالفعل؟
              <a href="/auth/login" class="link-primary fw-semibold">تسجيل الدخول</a>
            </div>
            <!--end::Sign up-->
          </form>
          <!--end::Form-->
        </div>
        <!--end::Card body-->
      </div>
      <!--end::Card-->
    </div>
    <!--end::Body-->
  </div>
  <!--end::Authentication - Sign-up-->
</div>
<!--end::Root-->
