{"ast": null, "code": "import arrayReduceRight from './_arrayReduceRight.js';\nimport baseEachRight from './_baseEachRight.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseReduce from './_baseReduce.js';\nimport isArray from './isArray.js';\n\n/**\n * This method is like `_.reduce` except that it iterates over elements of\n * `collection` from right to left.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @returns {*} Returns the accumulated value.\n * @see _.reduce\n * @example\n *\n * var array = [[0, 1], [2, 3], [4, 5]];\n *\n * _.reduceRight(array, function(flattened, other) {\n *   return flattened.concat(other);\n * }, []);\n * // => [4, 5, 2, 3, 0, 1]\n */\nfunction reduceRight(collection, iteratee, accumulator) {\n  var func = isArray(collection) ? arrayReduceRight : baseReduce,\n    initAccum = arguments.length < 3;\n  return func(collection, baseIteratee(iteratee, 4), accumulator, initAccum, baseEachRight);\n}\nexport default reduceRight;", "map": {"version": 3, "names": ["arrayReduceRight", "baseEachRight", "baseIteratee", "baseReduce", "isArray", "reduceRight", "collection", "iteratee", "accumulator", "func", "initAccum", "arguments", "length"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/reduceRight.js"], "sourcesContent": ["import arrayReduceRight from './_arrayReduceRight.js';\nimport baseEachRight from './_baseEachRight.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseReduce from './_baseReduce.js';\nimport isArray from './isArray.js';\n\n/**\n * This method is like `_.reduce` except that it iterates over elements of\n * `collection` from right to left.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The initial value.\n * @returns {*} Returns the accumulated value.\n * @see _.reduce\n * @example\n *\n * var array = [[0, 1], [2, 3], [4, 5]];\n *\n * _.reduceRight(array, function(flattened, other) {\n *   return flattened.concat(other);\n * }, []);\n * // => [4, 5, 2, 3, 0, 1]\n */\nfunction reduceRight(collection, iteratee, accumulator) {\n  var func = isArray(collection) ? arrayReduceRight : baseReduce,\n      initAccum = arguments.length < 3;\n\n  return func(collection, baseIteratee(iteratee, 4), accumulator, initAccum, baseEachRight);\n}\n\nexport default reduceRight;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EACtD,IAAIC,IAAI,GAAGL,OAAO,CAACE,UAAU,CAAC,GAAGN,gBAAgB,GAAGG,UAAU;IAC1DO,SAAS,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC;EAEpC,OAAOH,IAAI,CAACH,UAAU,EAAEJ,YAAY,CAACK,QAAQ,EAAE,CAAC,CAAC,EAAEC,WAAW,EAAEE,SAAS,EAAET,aAAa,CAAC;AAC3F;AAEA,eAAeI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}