{"ast": null, "code": "import arrayPush from './_arrayPush.js';\nimport getPrototype from './_getPrototype.js';\nimport getSymbols from './_getSymbols.js';\nimport stubArray from './stubArray.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own and inherited enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbolsIn = !nativeGetSymbols ? stubArray : function (object) {\n  var result = [];\n  while (object) {\n    arrayPush(result, getSymbols(object));\n    object = getPrototype(object);\n  }\n  return result;\n};\nexport default getSymbolsIn;", "map": {"version": 3, "names": ["arrayPush", "getPrototype", "getSymbols", "stubArray", "nativeGetSymbols", "Object", "getOwnPropertySymbols", "getSymbolsIn", "object", "result"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_getSymbolsIn.js"], "sourcesContent": ["import arrayPush from './_arrayPush.js';\nimport getPrototype from './_getPrototype.js';\nimport getSymbols from './_getSymbols.js';\nimport stubArray from './stubArray.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own and inherited enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbolsIn = !nativeGetSymbols ? stubArray : function(object) {\n  var result = [];\n  while (object) {\n    arrayPush(result, getSymbols(object));\n    object = getPrototype(object);\n  }\n  return result;\n};\n\nexport default getSymbolsIn;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,gBAAgB,GAAGC,MAAM,CAACC,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,CAACH,gBAAgB,GAAGD,SAAS,GAAG,UAASK,MAAM,EAAE;EAClE,IAAIC,MAAM,GAAG,EAAE;EACf,OAAOD,MAAM,EAAE;IACbR,SAAS,CAACS,MAAM,EAAEP,UAAU,CAACM,MAAM,CAAC,CAAC;IACrCA,MAAM,GAAGP,YAAY,CAACO,MAAM,CAAC;EAC/B;EACA,OAAOC,MAAM;AACf,CAAC;AAED,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}