<div class="card card-xl-stretch">
  <div class="card-header border-0 bg-light">
    <h3 class="card-title align-items-start flex-column">
      <span class="card-label fw-bold fs-3 mb-1 text-dark">{{ title }}</span>
      <span class="text-muted mt-1 fw-semibold fs-7">{{ subtitle }}</span>
    </h3>
  </div>



  <div class="card-body p-4">
    <div class="d-flex justify-content-center align-items-center mb-4">
      <div id="kt_card_widget_17_chart" [ngStyle]="{
          'min-width': chartSize + 'px',
          'min-height': chartSize + 'px'
        }" [attr.data-kt-size]="chartSize" [attr.data-kt-line]="chartLine"></div>
    </div>

    <div class="legend-container">
      <div class="row g-3">
        <div class="col-6">
          <div class="legend-item">
            <div class="bullet bg-warning"></div>
            <span class="legend-text">New {{ newCount }}</span>
          </div>
        </div>
        <div class="col-6">
          <div class="legend-item">
            <div class="bullet bg-success"></div>
            <span class="legend-text">Available {{ availableCount }}</span>
          </div>
        </div>
        <div class="col-6">
          <div class="legend-item">
            <div class="bullet bg-primary"></div>
            <span class="legend-text">Sold {{ soldCount }}</span>
          </div>
        </div>
        <div class="col-6">
          <div class="legend-item">
            <div class="bullet bg-danger"></div>
            <span class="legend-text">Reserved {{ reservedCount }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>