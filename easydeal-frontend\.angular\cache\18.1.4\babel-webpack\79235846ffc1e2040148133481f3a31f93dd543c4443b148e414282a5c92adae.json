{"ast": null, "code": "import baseIsNative from './_baseIsNative.js';\nimport isMaskable from './_isMaskable.js';\n\n/** Error message constants. */\nvar CORE_ERROR_TEXT = 'Unsupported core-js use. Try https://npms.io/search?q=ponyfill.';\n\n/**\n * Checks if `value` is a pristine native function.\n *\n * **Note:** This method can't reliably detect native functions in the presence\n * of the core-js package because core-js circumvents this kind of detection.\n * Despite multiple requests, the core-js maintainer has made it clear: any\n * attempt to fix the detection will be obstructed. As a result, we're left\n * with little choice but to throw an error. Unfortunately, this also affects\n * packages, like [babel-polyfill](https://www.npmjs.com/package/babel-polyfill),\n * which rely on core-js.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n * @example\n *\n * _.isNative(Array.prototype.push);\n * // => true\n *\n * _.isNative(_);\n * // => false\n */\nfunction isNative(value) {\n  if (isMaskable(value)) {\n    throw new Error(CORE_ERROR_TEXT);\n  }\n  return baseIsNative(value);\n}\nexport default isNative;", "map": {"version": 3, "names": ["baseIsNative", "isMaskable", "CORE_ERROR_TEXT", "isNative", "value", "Error"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/isNative.js"], "sourcesContent": ["import baseIsNative from './_baseIsNative.js';\nimport isMaskable from './_isMaskable.js';\n\n/** Error message constants. */\nvar CORE_ERROR_TEXT = 'Unsupported core-js use. Try https://npms.io/search?q=ponyfill.';\n\n/**\n * Checks if `value` is a pristine native function.\n *\n * **Note:** This method can't reliably detect native functions in the presence\n * of the core-js package because core-js circumvents this kind of detection.\n * Despite multiple requests, the core-js maintainer has made it clear: any\n * attempt to fix the detection will be obstructed. As a result, we're left\n * with little choice but to throw an error. Unfortunately, this also affects\n * packages, like [babel-polyfill](https://www.npmjs.com/package/babel-polyfill),\n * which rely on core-js.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a native function,\n *  else `false`.\n * @example\n *\n * _.isNative(Array.prototype.push);\n * // => true\n *\n * _.isNative(_);\n * // => false\n */\nfunction isNative(value) {\n  if (isMaskable(value)) {\n    throw new Error(CORE_ERROR_TEXT);\n  }\n  return baseIsNative(value);\n}\n\nexport default isNative;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,eAAe,GAAG,iEAAiE;;AAEvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,IAAIH,UAAU,CAACG,KAAK,CAAC,EAAE;IACrB,MAAM,IAAIC,KAAK,CAACH,eAAe,CAAC;EAClC;EACA,OAAOF,YAAY,CAACI,KAAK,CAAC;AAC5B;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}