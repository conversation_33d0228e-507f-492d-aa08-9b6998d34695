{"ast": null, "code": "import baseMerge from './_baseMerge.js';\nimport isObject from './isObject.js';\n\n/**\n * Used by `_.defaultsDeep` to customize its `_.merge` use to merge source\n * objects into destination objects that are passed thru.\n *\n * @private\n * @param {*} objValue The destination value.\n * @param {*} srcValue The source value.\n * @param {string} key The key of the property to merge.\n * @param {Object} object The parent object of `objValue`.\n * @param {Object} source The parent object of `srcValue`.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n * @returns {*} Returns the value to assign.\n */\nfunction customDefaultsMerge(objValue, srcValue, key, object, source, stack) {\n  if (isObject(objValue) && isObject(srcValue)) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, objValue);\n    baseMerge(objValue, srcValue, undefined, customDefaultsMerge, stack);\n    stack['delete'](srcValue);\n  }\n  return objValue;\n}\nexport default customDefaultsMerge;", "map": {"version": 3, "names": ["baseMerge", "isObject", "customDefaultsMerge", "objValue", "srcValue", "key", "object", "source", "stack", "set", "undefined"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_customDefaultsMerge.js"], "sourcesContent": ["import baseMerge from './_baseMerge.js';\nimport isObject from './isObject.js';\n\n/**\n * Used by `_.defaultsDeep` to customize its `_.merge` use to merge source\n * objects into destination objects that are passed thru.\n *\n * @private\n * @param {*} objValue The destination value.\n * @param {*} srcValue The source value.\n * @param {string} key The key of the property to merge.\n * @param {Object} object The parent object of `objValue`.\n * @param {Object} source The parent object of `srcValue`.\n * @param {Object} [stack] Tracks traversed source values and their merged\n *  counterparts.\n * @returns {*} Returns the value to assign.\n */\nfunction customDefaultsMerge(objValue, srcValue, key, object, source, stack) {\n  if (isObject(objValue) && isObject(srcValue)) {\n    // Recursively merge objects and arrays (susceptible to call stack limits).\n    stack.set(srcValue, objValue);\n    baseMerge(objValue, srcValue, undefined, customDefaultsMerge, stack);\n    stack['delete'](srcValue);\n  }\n  return objValue;\n}\n\nexport default customDefaultsMerge;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EAC3E,IAAIP,QAAQ,CAACE,QAAQ,CAAC,IAAIF,QAAQ,CAACG,QAAQ,CAAC,EAAE;IAC5C;IACAI,KAAK,CAACC,GAAG,CAACL,QAAQ,EAAED,QAAQ,CAAC;IAC7BH,SAAS,CAACG,QAAQ,EAAEC,QAAQ,EAAEM,SAAS,EAAER,mBAAmB,EAAEM,KAAK,CAAC;IACpEA,KAAK,CAAC,QAAQ,CAAC,CAACJ,QAAQ,CAAC;EAC3B;EACA,OAAOD,QAAQ;AACjB;AAEA,eAAeD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}