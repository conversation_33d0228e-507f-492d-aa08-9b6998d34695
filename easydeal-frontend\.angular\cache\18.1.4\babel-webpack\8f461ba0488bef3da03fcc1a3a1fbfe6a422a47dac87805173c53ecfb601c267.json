{"ast": null, "code": "import baseCreate from './_baseCreate.js';\nimport isObject from './isObject.js';\n\n/**\n * Creates a function that produces an instance of `Ctor` regardless of\n * whether it was invoked as part of a `new` expression or by `call` or `apply`.\n *\n * @private\n * @param {Function} Ctor The constructor to wrap.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createCtor(Ctor) {\n  return function () {\n    // Use a `switch` statement to work with class constructors. See\n    // http://ecma-international.org/ecma-262/7.0/#sec-ecmascript-function-objects-call-thisargument-argumentslist\n    // for more details.\n    var args = arguments;\n    switch (args.length) {\n      case 0:\n        return new Ctor();\n      case 1:\n        return new Ctor(args[0]);\n      case 2:\n        return new Ctor(args[0], args[1]);\n      case 3:\n        return new Ctor(args[0], args[1], args[2]);\n      case 4:\n        return new Ctor(args[0], args[1], args[2], args[3]);\n      case 5:\n        return new Ctor(args[0], args[1], args[2], args[3], args[4]);\n      case 6:\n        return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5]);\n      case 7:\n        return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5], args[6]);\n    }\n    var thisBinding = baseCreate(Ctor.prototype),\n      result = Ctor.apply(thisBinding, args);\n\n    // Mimic the constructor's `return` behavior.\n    // See https://es5.github.io/#x13.2.2 for more details.\n    return isObject(result) ? result : thisBinding;\n  };\n}\nexport default createCtor;", "map": {"version": 3, "names": ["baseCreate", "isObject", "createCtor", "Ctor", "args", "arguments", "length", "thisBinding", "prototype", "result", "apply"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_createCtor.js"], "sourcesContent": ["import baseCreate from './_baseCreate.js';\nimport isObject from './isObject.js';\n\n/**\n * Creates a function that produces an instance of `Ctor` regardless of\n * whether it was invoked as part of a `new` expression or by `call` or `apply`.\n *\n * @private\n * @param {Function} Ctor The constructor to wrap.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createCtor(Ctor) {\n  return function() {\n    // Use a `switch` statement to work with class constructors. See\n    // http://ecma-international.org/ecma-262/7.0/#sec-ecmascript-function-objects-call-thisargument-argumentslist\n    // for more details.\n    var args = arguments;\n    switch (args.length) {\n      case 0: return new Ctor;\n      case 1: return new Ctor(args[0]);\n      case 2: return new Ctor(args[0], args[1]);\n      case 3: return new Ctor(args[0], args[1], args[2]);\n      case 4: return new Ctor(args[0], args[1], args[2], args[3]);\n      case 5: return new Ctor(args[0], args[1], args[2], args[3], args[4]);\n      case 6: return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5]);\n      case 7: return new Ctor(args[0], args[1], args[2], args[3], args[4], args[5], args[6]);\n    }\n    var thisBinding = baseCreate(Ctor.prototype),\n        result = Ctor.apply(thisBinding, args);\n\n    // Mimic the constructor's `return` behavior.\n    // See https://es5.github.io/#x13.2.2 for more details.\n    return isObject(result) ? result : thisBinding;\n  };\n}\n\nexport default createCtor;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO,YAAW;IAChB;IACA;IACA;IACA,IAAIC,IAAI,GAAGC,SAAS;IACpB,QAAQD,IAAI,CAACE,MAAM;MACjB,KAAK,CAAC;QAAE,OAAO,IAAIH,IAAI,CAAD,CAAC;MACvB,KAAK,CAAC;QAAE,OAAO,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;MAChC,KAAK,CAAC;QAAE,OAAO,IAAID,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;MACzC,KAAK,CAAC;QAAE,OAAO,IAAID,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;MAClD,KAAK,CAAC;QAAE,OAAO,IAAID,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;MAC3D,KAAK,CAAC;QAAE,OAAO,IAAID,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;MACpE,KAAK,CAAC;QAAE,OAAO,IAAID,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;MAC7E,KAAK,CAAC;QAAE,OAAO,IAAID,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;IACxF;IACA,IAAIG,WAAW,GAAGP,UAAU,CAACG,IAAI,CAACK,SAAS,CAAC;MACxCC,MAAM,GAAGN,IAAI,CAACO,KAAK,CAACH,WAAW,EAAEH,IAAI,CAAC;;IAE1C;IACA;IACA,OAAOH,QAAQ,CAACQ,MAAM,CAAC,GAAGA,MAAM,GAAGF,WAAW;EAChD,CAAC;AACH;AAEA,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}