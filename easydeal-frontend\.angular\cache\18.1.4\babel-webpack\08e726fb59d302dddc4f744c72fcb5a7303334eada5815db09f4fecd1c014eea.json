{"ast": null, "code": "import arrayMap from './_arrayMap.js';\nimport baseIntersection from './_baseIntersection.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport castArrayLikeObject from './_castArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.intersection` except that it accepts `iteratee`\n * which is invoked for each element of each `arrays` to generate the criterion\n * by which they're compared. The order and references of result values are\n * determined by the first array. The iteratee is invoked with one argument:\n * (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of intersecting values.\n * @example\n *\n * _.intersectionBy([2.1, 1.2], [2.3, 3.4], Math.floor);\n * // => [2.1]\n *\n * // The `_.property` iteratee shorthand.\n * _.intersectionBy([{ 'x': 1 }], [{ 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 1 }]\n */\nvar intersectionBy = baseRest(function (arrays) {\n  var iteratee = last(arrays),\n    mapped = arrayMap(arrays, castArrayLikeObject);\n  if (iteratee === last(mapped)) {\n    iteratee = undefined;\n  } else {\n    mapped.pop();\n  }\n  return mapped.length && mapped[0] === arrays[0] ? baseIntersection(mapped, baseIteratee(iteratee, 2)) : [];\n});\nexport default intersectionBy;", "map": {"version": 3, "names": ["arrayMap", "baseIntersection", "baseIteratee", "baseRest", "castArrayLikeObject", "last", "intersectionBy", "arrays", "iteratee", "mapped", "undefined", "pop", "length"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/intersectionBy.js"], "sourcesContent": ["import arrayMap from './_arrayMap.js';\nimport baseIntersection from './_baseIntersection.js';\nimport baseIteratee from './_baseIteratee.js';\nimport baseRest from './_baseRest.js';\nimport castArrayLikeObject from './_castArrayLikeObject.js';\nimport last from './last.js';\n\n/**\n * This method is like `_.intersection` except that it accepts `iteratee`\n * which is invoked for each element of each `arrays` to generate the criterion\n * by which they're compared. The order and references of result values are\n * determined by the first array. The iteratee is invoked with one argument:\n * (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {...Array} [arrays] The arrays to inspect.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns the new array of intersecting values.\n * @example\n *\n * _.intersectionBy([2.1, 1.2], [2.3, 3.4], Math.floor);\n * // => [2.1]\n *\n * // The `_.property` iteratee shorthand.\n * _.intersectionBy([{ 'x': 1 }], [{ 'x': 2 }, { 'x': 1 }], 'x');\n * // => [{ 'x': 1 }]\n */\nvar intersectionBy = baseRest(function(arrays) {\n  var iteratee = last(arrays),\n      mapped = arrayMap(arrays, castArrayLikeObject);\n\n  if (iteratee === last(mapped)) {\n    iteratee = undefined;\n  } else {\n    mapped.pop();\n  }\n  return (mapped.length && mapped[0] === arrays[0])\n    ? baseIntersection(mapped, baseIteratee(iteratee, 2))\n    : [];\n});\n\nexport default intersectionBy;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,mBAAmB,MAAM,2BAA2B;AAC3D,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAGH,QAAQ,CAAC,UAASI,MAAM,EAAE;EAC7C,IAAIC,QAAQ,GAAGH,IAAI,CAACE,MAAM,CAAC;IACvBE,MAAM,GAAGT,QAAQ,CAACO,MAAM,EAAEH,mBAAmB,CAAC;EAElD,IAAII,QAAQ,KAAKH,IAAI,CAACI,MAAM,CAAC,EAAE;IAC7BD,QAAQ,GAAGE,SAAS;EACtB,CAAC,MAAM;IACLD,MAAM,CAACE,GAAG,CAAC,CAAC;EACd;EACA,OAAQF,MAAM,CAACG,MAAM,IAAIH,MAAM,CAAC,CAAC,CAAC,KAAKF,MAAM,CAAC,CAAC,CAAC,GAC5CN,gBAAgB,CAACQ,MAAM,EAAEP,YAAY,CAACM,QAAQ,EAAE,CAAC,CAAC,CAAC,GACnD,EAAE;AACR,CAAC,CAAC;AAEF,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}