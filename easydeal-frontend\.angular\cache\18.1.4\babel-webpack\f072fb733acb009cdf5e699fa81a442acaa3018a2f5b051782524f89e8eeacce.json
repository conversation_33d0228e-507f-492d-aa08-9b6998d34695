{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SignupComponent } from './signup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SignupComponent\n}];\nexport class SignupRoutingModule {\n  static ɵfac = function SignupRoutingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SignupRoutingModule)();\n  };\n  static ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SignupRoutingModule\n  });\n  static ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SignupRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SignupComponent", "routes", "path", "component", "SignupRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\taskes\\fronLogin\\easydeal-frontend\\src\\app\\pages\\auth\\signup\\signup-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SignupComponent } from './signup.component';\n\nconst routes: Routes = [\n  {\n    path: '',\n    component: SignupComponent\n  }\n];\n\n@NgModule({\n  imports: [RouterModule.forChild(routes)],\n  exports: [RouterModule]\n})\nexport class SignupRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,eAAe,QAAQ,oBAAoB;;;AAEpD,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,mBAAmB;;qCAAnBA,mBAAmB;EAAA;;UAAnBA;EAAmB;;cAHpBL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;EAAA;;;2EAEXK,mBAAmB;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFpBT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}