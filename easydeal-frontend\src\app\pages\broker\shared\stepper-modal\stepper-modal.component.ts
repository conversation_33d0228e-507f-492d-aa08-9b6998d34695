import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { createrequestservice } from '../../services/createrequest.service';
import { PropertyService } from '../../services/property.service';
import Swal from 'sweetalert2';
import { HotelUnitRental } from 'src/app/models/createRequest.model ';
import { Router } from '@angular/router';


@Component({
  selector: 'app-stepper-modal',
  templateUrl: './stepper-modal.component.html',
  styleUrl: './stepper-modal.component.scss',
})
export class StepperModalComponent implements OnInit {
  totalSteps = 5;
  currentStep = 1;
  userId: number;

  // Error handling properties
  validationErrors: any[] = [];
  showErrorList = false;
  fieldToStepMap: { [key: string]: number } = {};
  stepNames: { [key: number]: string } = {
    1: 'Request Settings',
    2: 'Location Information',
    3: 'Unit Information',
    4: 'Project Documents',
    5: 'Financial Information'
  };

  cities: any[] = [];
  areas: any[] = [];
  subAreas: any[] = [];
  selectedCityId: number;
  selectedCityName: string;
  selectedAreaId: number;
  selectedAreaName: string;
  selectedSubAreaName: string;
  isLoadingCities = false;

  unitTypes: { key: string; value: string }[] = [];

  specializationScope: { key: string; value: string }[] = [
    {
      key: 'Purchase/Sell Outside Compound',
      value: 'purchase_sell_outside_compound',
    },
    {
      key: 'Purchase/Sell Inside Compound',
      value: 'purchase_sell_inside_compound',
    },
    { key: 'Primary Inside Compound', value: 'primary_inside_compound' },
    { key: 'Resale Inside Compound', value: 'resale_inside_compound' },
    { key: 'Rentals Outside Compound', value: 'rentals_outside_compound' },
    { key: 'Rentals Inside Compound', value: 'rentals_inside_compound' },
  ];

  Type: { key: string; value: string }[] = [
    { key: 'Sell', value: 'sell' },
    { key: 'Purchasing', value: 'purchasing' },
    { key: 'Rent Out', value: 'rent_out' },
    { key: 'Rent In', value: 'rent_in' },
  ];

  unitTypeOptions: { key: string; value: string }[] = [
    { key: 'Apartments', value: 'apartments' },
    { key: 'Duplexes', value: 'duplexes' },
    { key: 'Studios', value: 'studios' },
    { key: 'Penthouses', value: 'penthouses' },
    { key: 'Roofs', value: 'roofs' },
    { key: 'Villas', value: 'villas' },
    { key: 'I Villa', value: 'i_villa' },
    { key: 'Twin Houses', value: 'twin_houses' },
    { key: 'Town Houses', value: 'town_houses' },
    { key: 'Administrative Units', value: 'administrative_units' },
    { key: 'Medical Clinics', value: 'medical_clinics' },
    { key: 'Pharmacies', value: 'pharmacies' },
    { key: 'Commercial Stores', value: 'commercial_stores' },
    { key: 'Warehouses', value: 'warehouses' },
    { key: 'Factory Lands', value: 'factory_lands' },
    { key: 'Warehouses Land', value: 'warehouses_land' },
    { key: 'Standalone Villas', value: 'standalone_villas' },
    {
      key: 'Commercial Administrative Buildings',
      value: 'commercial_administrative_buildings',
    },
    {
      key: 'Commercial Administrative Lands',
      value: 'commercial_administrative_lands',
    },
    { key: 'Residential Buildings', value: 'residential_buildings' },
    { key: 'Residential Lands', value: 'residential_lands' },
    { key: 'Chalets', value: 'chalets' },
    { key: 'Hotels', value: 'hotels' },
    { key: 'Factories', value: 'factories' },
    { key: 'Basements', value: 'basements' },
    { key: 'Full Buildings', value: 'full_buildings' },
    { key: 'Commercial Units', value: 'commercial_units' },
    { key: 'Shops', value: 'shops' },
    { key: 'Mixed Housings', value: 'mixed_housings' },
    { key: 'Cooperatives', value: 'cooperatives' },
    { key: 'Youth Units', value: 'youth_units' },
    { key: 'Ganat Misr', value: 'ganat_misr' },
    { key: 'Dar Misr', value: 'dar_misr' },
    { key: 'Sakan Misr', value: 'sakan_misr' },
    { key: 'Industrial Lands', value: 'industrial_lands' },
    { key: 'Cabin', value: 'cabin' },
    { key: 'Chalets', value: 'chalets' },
    { key: 'Vacation Villa', value: 'vacation_villa' },
  ];

  floorTypes: { key: string; value: string }[] = [
    { key: 'Ground Floor', value: 'ground' },
    { key: 'Last Floor', value: 'last_floor' },
    { key: 'Repeated', value: 'repeated' },
    { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },
  ];

  buildingLicenseTypes: { key: string; value: string }[] = [
    { key: 'Permit Available', value: 'Permit_Available' },
    { key: 'No Permit', value: 'No_Permit' },
  ];

  unitLayoutStatusTypes: { key: string; value: string }[] = [
    { key: 'Partial Roof', value: 'partial_roof' },
    { key: 'Full Roof', value: 'full_roof' },
    { key: 'Open Space', value: 'open_space' },
    { key: 'Single Apartment', value: 'single_apartment' },
    { key: 'Two Apartments', value: 'two_apartments' },
    { key: 'All Acceptable', value: 'all_acceptable' },
  ];

  buildingLayoutStatusTypes: { key: string; value: string }[] = [
    { key: 'Open Space', value: 'open_space' },
    { key: 'Single Apartment', value: 'single_apartment' },
    { key: 'Two Apartments', value: 'two_apartments' },
    { key: 'All Acceptable', value: 'all_acceptable' },
  ];

  groundLayoutStatusTypes: { key: string; value: string }[] = [
    { key: 'Vacant Land', value: 'vacant_land' },
    { key: 'Under Construction', value: 'under_construction' },
    { key: 'Fully Built', value: 'fully_built' },
    { key: 'All Acceptable', value: 'all_acceptable' },
  ];

  unitDesignTypes: { key: string; value: string }[] = [
    { key: 'Custom Design', value: 'custom_design' },
    { key: 'One Apartment Per Floor', value: 'one_apartment_per_floor' },
    { key: 'Two Apartments Per Floor', value: 'two_apartments_per_floor' },
    {
      key: 'More Than Two Apartments Per Floor',
      value: 'more_than_two_apartments_per_floor',
    },
    { key: 'All Acceptable', value: 'all_acceptable' },
  ];

  activityTypes: { key: string; value: string }[] = [
    { key: 'Administrative Only', value: 'administrative_only' },
    { key: 'Commercial Only', value: 'commercial_only' },
    { key: 'Medical Only', value: 'medical_only' },
    {
      key: 'Administrative and Commercial',
      value: 'administrative_and_commercial',
    },
    {
      key: 'Administrative, Commercial and Medical',
      value: 'administrative_commercial_and_medical',
    },
  ];

  unitDescriptionTypes: { key: string; value: string }[] = [
    { key: 'Single Front', value: 'single_front' },
    { key: 'Corner', value: 'corner' },
    { key: 'Double Front', value: 'double_front' },
    { key: 'Triple Corner', value: 'triple_corner' },
    { key: 'Quad Corner', value: 'quad_corner' },
    { key: 'All Acceptable', value: 'all_acceptable' },
  ];

  subUnitTypeOptions: { key: string; value: string }[] = [
    { key: 'Apartments', value: 'apartments' },
    { key: 'Duplexes', value: 'duplexes' },
    { key: 'Studios', value: 'studios' },
    { key: 'Penthouses', value: 'penthouses' },
    { key: 'Basement', value: 'basement' },
    { key: 'Roofs', value: 'roofs' },
    { key: 'Villas', value: 'villas' },
    { key: 'I Villa', value: 'i_villa' },
    { key: 'Twin Houses', value: 'twin_houses' },
    { key: 'Town Houses', value: 'town_houses' },
    { key: 'Administrative Units', value: 'administrative_units' },
    { key: 'Medical Clinics', value: 'medical_clinics' },
    { key: 'Pharmacies', value: 'pharmacies' },
    { key: 'Commercial Stores', value: 'commercial_stores' },
    { key: 'Warehouses', value: 'warehouses' },
    { key: 'Factory Lands', value: 'factory_lands' },
    { key: 'Warehouses Land', value: 'warehouses_land' },
    { key: 'Standalone Villas', value: 'standalone_villas' },
    {
      key: 'Commercial Administrative Buildings',
      value: 'commercial_administrative_buildings',
    },
    {
      key: 'Commercial Administrative Lands',
      value: 'commercial_administrative_lands',
    },
    { key: 'Residential Buildings', value: 'residential_buildings' },
    { key: 'Residential Lands', value: 'residential_lands' },
    { key: 'Chalets', value: 'chalets' },
    { key: 'Hotels', value: 'hotels' },
    { key: 'Factories', value: 'factories' },
    { key: 'Basements', value: 'basements' },
    { key: 'Full Buildings', value: 'full_buildings' },
    { key: 'Commercial Units', value: 'commercial_units' },
    { key: 'Shops', value: 'shops' },
    { key: 'Mixed Housings', value: 'mixed_housings' },
    { key: 'Cooperatives', value: 'cooperatives' },
    { key: 'Youth Units', value: 'youth_units' },
    { key: 'Ganat Misr', value: 'ganat_misr' },
    { key: 'Dar Misr', value: 'dar_misr' },
    { key: 'Sakan Misr', value: 'sakan_misr' },
    { key: 'Industrial Lands', value: 'industrial_lands' },
    { key: 'Cabin', value: 'cabin' },
    { key: 'Chalets', value: 'chalets' },
    { key: 'Vacation Villa', value: 'vacation_villa' },
    { key: 'Hotel Units', value: 'hotel_units' },
  ];

  rentRecurrenceTypes: { key: string; value: string }[] = [
    { key: 'Monthly', value: 'monthly' },
    { key: 'Quarterly', value: 'quarterly' },
    { key: 'Semi Annually', value: 'semi_annually' },
    { key: 'Annually', value: 'annually' },
  ];

  paymentTypes: { key: string; value: string }[] = [
    { key: 'Cash', value: 'cash' },
    { key: 'Installment', value: 'installment' },
    { key: 'Both', value: 'all_of_the_above_are_suitable' },
  ];

  requiredInsuranceTypes: { key: string; value: string }[] = [
    { key: 'One Month', value: 'one_month' },
    { key: 'Two Months', value: 'two_months' },
    { key: 'Fixed Amount', value: 'fixed_amount' },
  ];

  otherExpensesTypes: { key: string; value: string }[] = [
    { key: 'Other', value: 'other' },
    { key: 'Electricity', value: 'electricity' },
    { key: 'Gas', value: 'gas' },
    { key: 'Water', value: 'water' },
    { key: 'Security Maintenance', value: 'security_maintenance' },
  ];

  furnishingStatusTypes: { key: string; value: string }[] = [
    { key: 'Unfurnished', value: 'unfurnished' },
    {
      key: 'Furnished with Air Conditioners',
      value: 'furnished_with_air_conditioners',
    },
    {
      key: 'Furnished without Air Conditioners',
      value: 'furnished_without_air_conditioners',
    },
  ];

  deliveryStatusTypes: { key: string; value: string }[] = [
    { key: 'Immediate Delivery', value: 'immediate_delivery' },
    { key: 'Under Construction', value: 'under_construction' },
  ];

  financialStatusTypes: { key: string; value: string }[] = [
    { key: 'Paid in Full', value: 'paid_in_full' },
    {
      key: 'Partially Paid with Remaining Installments',
      value: 'partially_paid_with_remaining_installments',
    },
  ];

  legalStatusTypes: { key: string; value: string }[] = [
    { key: 'Licensed', value: 'licensed' },
    { key: 'Reconciled', value: 'reconciled' },
    { key: 'Reconciliation Required', value: 'reconciliation_required' },
  ];

  fitOutConditionTypes: { key: string; value: string }[] = [
    { key: 'Unfitted', value: 'unfitted' },
    { key: 'Fully Fitted', value: 'fully_fitted' },
    { key: 'All The Above Are Suitable', value: 'all_the_above_are_suitable' },
  ];

  finishingStatusTypes: { key: string; value: string }[] = [
    { key: 'On Brick', value: 'on_brick' },
    { key: 'Semi Finished', value: 'semi_finished' },
    { key: 'Company Finished', value: 'company_finished' },
    { key: 'Super Lux', value: 'super_lux' },
    { key: 'Ultra Super Lux', value: 'ultra_super_lux' },
  ];

  unitViewTypes: { key: string; value: string }[] = [
    { key: 'Water View', value: 'water_view' },
    { key: 'Gardens and Landscape', value: 'gardens_and_landscape' },
    { key: 'Street', value: 'street' },
    { key: 'Entertainment Area', value: 'entertainment_area' },
    { key: 'Garden', value: 'garden' },
    { key: 'Main Street', value: 'main_street' },
    { key: 'Square', value: 'square' },
    { key: 'Side Street', value: 'side_street' },
    { key: 'Rear View', value: 'rear_view' },
  ];

  unitFacingTypes: { key: string; value: string }[] = [
    { key: 'Right of Facade', value: 'right_of_facade' },
    { key: 'Left of Facade', value: 'left_of_facade' },
    { key: 'Side View', value: 'side_view' },
    { key: 'Rear View', value: 'rear_view' },
  ];

  otherAccessoriesTypes: { key: string; value: string }[] = [
    { key: 'Garage', value: 'garage' },
    { key: 'Clubhouse', value: 'clubhouse' },
    { key: 'Club', value: 'club' },
    { key: 'Storage', value: 'storage' },
    { key: 'Elevator', value: 'elevator' },
    { key: 'Swimming Pool', value: 'swimming_pool' },
  ];

  buildingDeadlineTypes: { key: string; value: string }[] = [
    { key: 'Grace Period Allowed', value: 'Grace_Period_Allowed' },
    { key: 'No Grace Period', value: 'No_Grace_Period' },
  ];

  // Form groups for each step
  step1Form: FormGroup;
  step2Form: FormGroup;
  step3Form: FormGroup;
  step4Form: FormGroup;
  step5Form: FormGroup;
  // step6Form: FormGroup;

  constructor(
    private fb: FormBuilder,
    private createRequestService: createrequestservice,
    private propertyService: PropertyService,
    private cdr: ChangeDetectorRef,
    private router: Router,
  ) {}

  ngOnInit(): void {

    this.userId = 3;

    this.initForms();
    this.loadUnitTypes();
    this.loadCities();
    this.loadAreas();
    this.generateFieldMappings();
  }

   generateFieldMappings() {
     [this.step1Form, this.step2Form, this.step3Form, this.step4Form, this.step5Form]
      .forEach((form, index) => {
        if (form) {
          Object.keys(form.controls).forEach(field => {
            this.fieldToStepMap[field] = index + 1;
          });
        }
      });


  }



  initForms() {
    // Step 1: Basic Request Settings
    this.step1Form = this.fb.group({
      specializationScope: ['', Validators.required],
      type: ['', Validators.required],
      unitType: ['', Validators.required],
    });

    // Step 2: Location Information
    this.step2Form = this.fb.group({
      compoundName: [''],
      mallName: [''],
      cityId: ['', Validators.required],
      areaId: ['', Validators.required],
      subAreaId: [''],
      villageName: [''],
      detailedAddress: [''],
      addressLink: [
        '',
        [Validators.pattern(/^https?:\/\/.+/)],
      ],
      locationSuggestion: [false],
    });

    // Step 3: Unit Information
    this.step3Form = this.fb.group({
      unitNumber: [''],
      buildingNumber: [''],
      unitArea: [null],
      unitAreaMin: [null],
      unitAreaMax: [null],
      buildingArea: [null],
      buildingAreaMin: [null],
      buildingAreaMax: [null],
      groundArea: [null],
      groundAreaMin: [null],
      groundAreaMax: [null],
      gardenArea: [null],
      terraceArea: [null],
      rooms: [null], // توحيد الاسم
      bathRooms: [null], // توحيد الاسم
      numberOfFloors: [null],
      floor: [''],
      favoriteFloor: [''],
      buildingDeadline: [''],
      buildingLicense: [''],
      rentDuration: [null],
      rentDateMin: [''],
      rentDateMax: [''],
      unitLayoutStatus: [''],
      buildingLayoutStatus: [''],
      groundLayoutStatus: [''],
      unitDesign: [''],
      activity: [''],
      unitDescription: [''],
      subUnitType: [''],
      rentRecurrence: [''],
      furnishingStatus: [''],
      deliveryStatus: [''],
      financialStatus: [''], // توحيد الاسم
      legalStatus: [''],
      fitOutCondition: [''],
      finishingStatus: [''],
      unitView: [''],
      unitFacing: [''],
      otherAccessories: [[]],
      areaSuggestions: [false],
      buildingAreaSuggestions: [false],
      notes: [''],
    });

    // Step 4: Project Documents
    this.step4Form = this.fb.group({
      mainImage: [[]],
      galleryImages: [[]],
      video: [[]],
      unitInMasterPlanImage: [[]],
    });

    // Step 5: Payment System
    this.step5Form = this.fb.group({
      paymentMethod: [''],
      averageUnitPriceMin: [null, Validators.min(0)],
      averageUnitPriceMax: [null, Validators.min(0)],
      unitPriceSuggestions: [false], // توحيد الاسم
      averageUnitPriceMonthlyMin: [null, Validators.min(0)],
      averageUnitPriceMonthlyMax: [null, Validators.min(0)],
      averageUnitPriceMonthlySuggestions: [false],
      averageUnitPriceDailyMin: [null, Validators.min(0)],
      averageUnitPriceDailyMax: [null, Validators.min(0)],
      averageUnitPriceDailySuggestions: [false],
      unitPrice: [null, Validators.min(0)],
      requestedOver: [null, Validators.min(0)],
      requiredInsurance: [''],
      otherExpenses: [[]],
      // averageUnitPrice: [null, Validators.min(0)],
      askingPrice: [null, Validators.min(0)],
    });
  }

  getCurrentForm(): FormGroup {
    switch (this.currentStep) {
      case 1:
        return this.step1Form;
      case 2:
        return this.step2Form;
      case 3:
        return this.step3Form;
      case 4:
        return this.step4Form;
      case 5:
        return this.step5Form;
      // case 6:
      //   return this.step6Form;
      default:
        return this.step1Form;
    }
  }

  loadUnitTypes(): void {
    this.propertyService.getUnitTypes().subscribe({
      next: (response) => {
        this.unitTypes = Object.entries(response.data).map(([key, value]) => ({
          key,
          value: value as string,
        }));
        console.log('Unit Types loaded:', this.unitTypes);
      },
      error: (err) => {
        console.error('Error loading unitTypes:', err);
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  loadCities(): void {
    this.isLoadingCities = true;
    this.propertyService.getCities().subscribe({
      next: (response) => {
        if (response && response.data) {
          this.cities = response.data;
        } else {
          console.warn('No cities data in response');
          this.cities = [];
        }
      },
      error: (err) => {
        console.error('Error loading cities:', err);
      },
      complete: () => {
        this.isLoadingCities = false;
        this.cdr.detectChanges();
      },
    });
  }

  loadAreas(cityId?: number): void {
    this.propertyService.getAreas(cityId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.areas = response.data;
        } else {
          console.warn('No areas data in response');
          this.areas = [];
        }
      },
      error: (err) => {
        console.error('Error loading areas:', err);
        this.areas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  selectCity(cityId: number, cityName: string) {
    this.selectedCityId = cityId;
    this.selectedCityName = cityName;
    this.step2Form.patchValue({
      cityId: cityId,
    });
    this.loadAreas(cityId);
  }

  selectArea(areaId: number, areaName: string) {
    this.selectedAreaId = areaId;
    this.selectedAreaName = areaName;
    this.step2Form.patchValue({
      areaId: areaId,
    });

    this.loadSubAreas(areaId);
  }

  selectSubArea(subAreaId: number, subAreaName: string) {
    this.selectedSubAreaName = subAreaName;
    this.step2Form.patchValue({
      subAreaId: subAreaId,
    });
  }

  loadSubAreas(areaId?: number): void {
    this.propertyService.gitsubAreas(areaId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.subAreas = response.data;
        } else {
          console.warn('No sub-areas data in response');
          this.subAreas = [];
        }
      },
      error: (err) => {
        console.error('Error loading sub-areas:', err);
        this.subAreas = [];
      },
      complete: () => {
        this.cdr.detectChanges();
      },
    });
  }

  getText(array: { key: string; value: string }[], value: string): string {
    const item = array.find((item) => item.value === value);
    return item ? item.key : '';
  }

  select(form: FormGroup, field: string, value: string) {
    form.patchValue({ [field]: value });
  }

  // Other Accessories methods
  getSelectedAccessoriesText(): string {
    const selectedAccessories = this.otherAccessoriesTypes.filter((accessory) =>
      this.isAccessorySelected(accessory.value)
    );

    if (selectedAccessories.length === 0) {
      return '';
    } else if (selectedAccessories.length === 1) {
      return selectedAccessories[0].key;
    } else {
      return `${selectedAccessories.length} accessories selected`;
    }
  }

  isAccessorySelected(accessoryValue: string): boolean {
    const otherAccessories =
      this.step3Form.get('otherAccessories')?.value || [];
    return otherAccessories.includes(accessoryValue);
  }

  toggleAccessory(accessoryValue: string) {
    const currentAccessories =
      this.step3Form.get('otherAccessories')?.value || [];
    let updatedAccessories;

    if (currentAccessories.includes(accessoryValue)) {
      updatedAccessories = currentAccessories.filter(
        (item: string) => item !== accessoryValue
      );
    } else {
      updatedAccessories = [...currentAccessories, accessoryValue];
    }

    this.step3Form.patchValue({ otherAccessories: updatedAccessories });
  }

  onAllAccessoriesChange(event: any) {
    const isChecked = event.target.checked;

    if (isChecked) {
      const allAccessories = this.otherAccessoriesTypes.map(
        (accessory) => accessory.value
      );
      this.step3Form.patchValue({ otherAccessories: allAccessories });
    } else {
      this.step3Form.patchValue({ otherAccessories: [] });
    }
  }

  // File upload methods
  onFileChange(event: any, fieldName: string) {
    if (event.target.files && event.target.files.length) {
      const files = Array.from(event.target.files);
      this.step4Form.patchValue({
        [fieldName]: files,
      });

      console.log(`${fieldName}: ${files.length} files selected`);
    }
  }

  getFileCount(fieldName: string): number {
    const files = this.step4Form.get(fieldName)?.value;
    return files && Array.isArray(files) ? files.length : 0;
  }

  // Payment system methods
  shouldShowCashFields(): boolean {
    const paymentMethod = this.step5Form.get('paymentMethod')?.value;
    return paymentMethod === 'cash' || paymentMethod === 'both';
  }

  shouldShowInstallmentFields(): boolean {
    const paymentMethod = this.step5Form.get('paymentMethod')?.value;
    return paymentMethod === 'installment' || paymentMethod === 'both';
  }

  // Other Expenses methods
  getSelectedOtherExpensesText(): string {
    const selectedExpenses = this.otherExpensesTypes.filter((expense) =>
      this.isOtherExpenseSelected(expense.value)
    );

    if (selectedExpenses.length === 0) {
      return '';
    } else if (selectedExpenses.length === 1) {
      return selectedExpenses[0].key;
    } else {
      return `${selectedExpenses.length} expenses selected`;
    }
  }

  isOtherExpenseSelected(expenseValue: string): boolean {
    const otherExpenses = this.step5Form.get('otherExpenses')?.value || [];
    return otherExpenses.includes(expenseValue);
  }

  toggleOtherExpense(expenseValue: string) {
    const currentExpenses = this.step5Form.get('otherExpenses')?.value || [];
    let updatedExpenses;

    if (currentExpenses.includes(expenseValue)) {
      updatedExpenses = currentExpenses.filter(
        (item: string) => item !== expenseValue
      );
    } else {
      updatedExpenses = [...currentExpenses, expenseValue];
    }

    this.step5Form.patchValue({ otherExpenses: updatedExpenses });
  }

  // Simple error handling
  handleSubmissionError(error: any) {
    this.validationErrors = [];
    this.showErrorList = false;

    if (error?.error?.errors) {
      Object.keys(this.fieldToStepMap).length === 0 && this.generateFieldMappings();

      Object.keys(error.error.errors).forEach(fieldKey => {
        const cleanField = fieldKey.replace(/^attributes\./, '').replace(/\[\d+\].*/, '');

        const step = this.fieldToStepMap[cleanField] || 1;

        let stepGroup = this.validationErrors.find(v => v.step === step);
        if (!stepGroup) {
          stepGroup = { step, stepName: this.stepNames[step], errors: [] };
          this.validationErrors.push(stepGroup);
        }

        stepGroup.errors.push({
          field: cleanField,
          messages: Array.isArray(error.error.errors[fieldKey]) ? error.error.errors[fieldKey] : [error.error.errors[fieldKey]]
        });
      });

      this.validationErrors.sort((a, b) => a.step - b.step);
      this.showErrorList = true;

       this.cdr.detectChanges();


    } else {
      Swal.fire('Error', error?.error?.message || 'An unknown error occurred.', 'error');
    }
  }

  clearValidationErrors() {
    this.validationErrors = [];
    this.showErrorList = false;
  }

  navigateToErrorStep(stepNumber: number) {
    this.currentStep = stepNumber;

  }

  // Submit the form
  submitForm() {
    if (this.isCurrentFormValid()) {
      const formData: HotelUnitRental = {
        ...this.step1Form.value,
        ...this.step2Form.value,
        ...this.step3Form.value,
        ...this.step4Form.value,
        ...this.step5Form.value,
        // ...this.step6Form.value,
      };

      const httpFormData = new FormData();

      const step1Values = this.step1Form.value;
      httpFormData.append(
        'specializationScope',
        step1Values.specializationScope || ''
      );
      httpFormData.append('type', step1Values.type || '');
      httpFormData.append('unitType', step1Values.unitType || '');

       httpFormData.append('userId', this.userId.toString());

       const step2Values = this.step2Form.value;
      if (step2Values.cityId) {
        httpFormData.append('locations[0][city]', step2Values.cityId);
      }
      if (step2Values.areaId) {
        httpFormData.append('locations[0][areas][0][id]', step2Values.areaId);
      }

      // Handle sub-areas array
      if (step2Values.subAreaId) {
         httpFormData.append(
          'locations[0][areas][0][subAreas][0]',
          step2Values.subAreaId
        );
      }

      // Combine all form values automatically
      let allFormData = {
        ...step2Values,
        ...this.step3Form.value,
        ...this.step5Form.value,
        // ...this.step6Form.value,
      };

      // Create attributes object directly (no mapping needed)
      const attributeFields: any = {};

      Object.keys(allFormData).forEach((key) => {
        attributeFields[key] = allFormData[key as keyof typeof allFormData];
      });


      // Add all attribute fields to FormData
      Object.keys(attributeFields).forEach((key) => {
        const value = attributeFields[key as keyof typeof attributeFields];
        if (value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach((item, index) => {
              httpFormData.append(`attributes[${key}][${index}]`, item);
            });
          } else if (typeof value === 'boolean') {
            httpFormData.append(`attributes[${key}]`, value ? '1' : '0');
          } else if (value !== '') {
            httpFormData.append(`attributes[${key}]`, value.toString());
          }
        }
      });

      // Add files to attributes
      const fileFields = [
        'mainImage',
        'galleryImages',
        'video',
        'unitInMasterPlanImage',
      ];
      fileFields.forEach((field) => {
        const files = this.step4Form.get(field)?.value;
        if (files && files.length) {
          const isMultiple = ['galleryImages', 'video'].includes(field);

          if (isMultiple) {
            files.forEach((file: File, index: number) => {
              httpFormData.append(`attributes[${field}][${index}]`, file);
            });
          } else {
            httpFormData.append(`attributes[${field}]`, files[0]);
          }
        }
      });

      // Submit to backend
      this.createRequestService.createRequest(httpFormData).subscribe({
        next: async (response) => {
          console.log('Request submitted successfully:', response);
          this.clearValidationErrors();
          await Swal.fire('Request submitted successfully!', '', 'success');

          this.router.navigate(['/requests']);
        },
        error: (err) => {

          this.handleSubmissionError(err);
        },
        complete: () => {
          this.cdr.detectChanges();

        },
      });
    }
  }

  // Check if current form is valid
  isCurrentFormValid(): boolean {
    return this.getCurrentForm().valid;
  }

  // Navigate to next step
  nextStep() {
    if (this.currentStep < this.totalSteps) {
      this.currentStep++;
      // Keep validation errors visible when navigating
    }
  }

  // Navigate to previous step
  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
      // Keep validation errors visible when navigating
    }
  }

  getInsideCompoundPrivilege() {
    const scopes = this.step1Form.get('specializationScope')?.value || [];
    const targetScopes = [
      'purchase_sell_inside_compound',
      'primary_inside_compound',
      'resale_inside_compound',
      'rentals_inside_compound'
    ];
    return targetScopes.some(scope => scopes.includes(scope));
  }

  getMallPrivilege() {
  const typeValues = this.step1Form.get('type')?.value || [];
  const unitType = this.step1Form.get('unitType')?.value;
  const mallUnitTypes = [
    'administrative_units',
    'medical_clinics',
    'pharmacies',
    'commercial_stores'
  ];

  const hasTargetType = ['sell', 'rent_out'].some(type => typeValues.includes(type));
  return mallUnitTypes.includes(unitType) && hasTargetType && !this.getInsideCompoundPrivilege();
  }

  getUnitAndBuildingNumber(){
    const typeValues = this.step1Form.get('type')?.value || [];
    const mallUnitTypes = ['sell', 'rent_out'];
    return mallUnitTypes.some(type => typeValues.includes(type));
  }

  getVillageName(){
     const unitType = this.step1Form.get('unitType')?.value;
      const mallUnitTypes = [
    'vacation_villa',
    'chalets',
  ];
  return mallUnitTypes.includes(unitType);
  }

  getUnitAreaPrivilege(){
  const typeValues = this.step1Form.get('type')?.value || [];
  const unitType = this.step1Form.get('unitType')?.value;
  const checkedUnitTypes = [
    'standalone_villas',
    'twin_houses',
    'town_houses',
    'residential_buildings',
    'commercial_administrative_buildings',
    'warehouses',
    'factory_lands',
    'hotels',
    'chalets',
  ];

  const hasTargetType = ['sell', 'rent_out'].some(type => typeValues.includes(type));
  const isCheckedUnitType = checkedUnitTypes.includes(unitType);

  return (!this.getInsideCompoundPrivilege() && hasTargetType && !isCheckedUnitType) ||
    (typeValues.includes('rent_out') && this.getInsideCompoundPrivilege()) ||
    (this.getInsideCompoundPrivilege() && typeValues.includes('sell') && !isCheckedUnitType);
}

  getUnitAreaMinPrivilege(){
   const typeValues = this.step1Form.get('type')?.value || [];
    const unitType = this.step1Form.get('unitType')?.value;
    const checkedUnitTypes1 = [
      'standalone_villas',
      'residential_buildings',
      'commercial_administrative_buildings',
      'warehouses',
      'factory_lands',
    ];
    const checkedUnitTypes2 = [
      'vacation_villa',
      'chalets',
    ];

    const hasTargetType = ['purchasing','rent_in'].some(type => typeValues.includes(type));
    const isCheckedUnitType1 = checkedUnitTypes1.includes(unitType);
    const isCheckedUnitType2 = checkedUnitTypes2.includes(unitType);

    return  (hasTargetType && this.getInsideCompoundPrivilege())  ||
      (hasTargetType && !this.getInsideCompoundPrivilege() && !isCheckedUnitType1) ||
      (typeValues.includes('purchasing') && isCheckedUnitType2);
  }

  getSpecificValuePrivilege(){
   const typeValues = this.step1Form.get('type')?.value || [];
    const unitType = this.step1Form.get('unitType')?.value;
    const checkedUnitTypes = [
      'standalone_villas',
      'twin_houses',
      'town_houses',
      'residential_buildings',
      'commercial_administrative_buildings',
      'warehouses',
      'factory_lands',
      'hotels',
      'chalets',
    ];

    const hasTargetType = ['sell', 'rent_out'].some(type => typeValues.includes(type));
    const isCheckedUnitType = checkedUnitTypes.includes(unitType);

    return (!this.getInsideCompoundPrivilege() && hasTargetType && isCheckedUnitType) ||
        (isCheckedUnitType && typeValues.includes('sell') && this.getInsideCompoundPrivilege());
  }

 getGroundAreaPrivilege(){
   const typeValues = this.step1Form.get('type')?.value || [];
    const unitType = this.step1Form.get('unitType')?.value;
    const checkedUnitTypes = [
      'standalone_villas',
      'twin_houses',
      'town_houses',
      'residential_buildings',
      'commercial_administrative_buildings',
      'warehouses',
      'factory_lands'
      ];

    const hasTargetType = ['sell', 'rent_out'].some(type => typeValues.includes(type));
    const isCheckedUnitType = checkedUnitTypes.includes(unitType);
    return (hasTargetType && isCheckedUnitType);
  }

 getGardenAreaPrivilege(){
    const typeValues = this.step1Form.get('type')?.value || [];
    const unitType = this.step1Form.get('unitType')?.value;
    const checkedUnitTypes = [
      'villas',
      'duplexes',

    ];

    const hasTargetType = ['sell', 'rent_out'].some(type => typeValues.includes(type));
    const isCheckedUnitType = checkedUnitTypes.includes(unitType);
    return (this.getInsideCompoundPrivilege() && hasTargetType && isCheckedUnitType) ||
          (isCheckedUnitType && typeValues.includes('sell') && !this.getInsideCompoundPrivilege());
  }

  getTerraceAreaPrivilege(){
    const typeValues = this.step1Form.get('type')?.value || [];
    const unitType = this.step1Form.get('unitType')?.value;
    const checkedUnitTypes = [
      'villas',
      'penthouses',
    ];

    const hasTargetType = ['sell', 'rent_out'].some(type => typeValues.includes(type));
    const isCheckedUnitType = checkedUnitTypes.includes(unitType);
    return (this.getInsideCompoundPrivilege() && hasTargetType && isCheckedUnitType) ||
          (isCheckedUnitType && typeValues.includes('sell') && !this.getInsideCompoundPrivilege());
  }

  getBuildingAreaMinPrivilege(){
    const typeValues = this.step1Form.get('type')?.value || [];
    const unitType = this.step1Form.get('unitType')?.value;
    const checkedUnitTypes = [
      'standalone_villas',
      'twin_houses',
      'town_houses',
      'commercial_administrative_buildings',
      'warehouses',
      'factory_lands'
    ];
    const checkedUnitTypes2 = ['chalets', 'vacation_villa'];

    const hasTargetType = ['purchasing', 'rent_in'].some(type => typeValues.includes(type));
    const isCheckedUnitType = checkedUnitTypes.includes(unitType);
    const isCheckedUnitType2 = checkedUnitTypes2.includes(unitType);

    return (!this.getInsideCompoundPrivilege() && hasTargetType && isCheckedUnitType) ||
        (isCheckedUnitType && typeValues.includes('purchasing') && this.getInsideCompoundPrivilege()) ||
        (isCheckedUnitType2 && typeValues.includes('purchasing'));
  }

  getGroundAreaMinPrivilege(){
    const typeValues = this.step1Form.get('type')?.value || [];
    const unitType = this.step1Form.get('unitType')?.value;
    const checkedUnitTypes = [
      'standalone_villas',
      'twin_houses',
      'town_houses',
      'residential_buildings',
      'commercial_administrative_buildings',
      'warehouses',
      'factory_lands'
    ];

    const hasTargetType = ['purchasing', 'rent_in'].some(type => typeValues.includes(type));
    const isCheckedUnitType = checkedUnitTypes.includes(unitType);

    return (!this.getInsideCompoundPrivilege() && hasTargetType && isCheckedUnitType) ||
        (isCheckedUnitType && typeValues.includes('rent_in') && this.getInsideCompoundPrivilege());
  }

  getNumberFloorPrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const mallUnitTypes = [
    'standalone_villas','residential_buildings','villas','i_villa','twin_houses',
    'town_houses','commercial_administrative_buildings','warehouses','factory_lands','vacation_villa'
    ];
   return mallUnitTypes.includes(unitType);
  }

  getFavoriteFloorPrivilege(){
    const typeValues = this.step1Form.get('type')?.value;
    const unitType = this.step1Form.get('unitType')?.value;
    const mallUnitTypes = [
    'standalone_villas','villas','penthouses','twin_houses',
    'town_houses','commercial_administrative_buildings'
    ];
    return !mallUnitTypes.includes(unitType) && typeValues.includes('rent_in') && this.getInsideCompoundPrivilege();
  }

  getAddressPrivilege(){
    const typeValues = this.step1Form.get('type')?.value;
    const hasTargetType = ['sell', 'rent_out'].some(type => typeValues.includes(type));
    return hasTargetType;
  }

  getBuildingPrivilege(){
    const type = this.step1Form.get('type')?.value.includes('sell');
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ['residential_buildings', 'commercial_administrative_buildings'].includes(unitType);
    return type && checkUnitTypes && !this.getInsideCompoundPrivilege();
  }

  getRentDurationPrivilege(){
    const type = this.step1Form.get('type')?.value.includes('rent_in');
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ['chalets' ,'hotels' ].includes(unitType);
    return type && checkUnitTypes ;
  }

  getFurnishingStatusPrivilege(){
    const typeValues = this.step1Form.get('type')?.value || [];
    const hasTargetType = ['rent_out', 'rent_in'].some(type => typeValues.includes(type));
    const unitType = this.step1Form.get('unitType')?.value;
    const unitTypes = [
    'pharmacies','commercial_stores','factory_lands','warehouses',
    'commercial_administrative_lands','commercial_administrative_buildings'
  ];
  return !unitTypes.includes(unitType) && hasTargetType;
  }

  getDeliveryStatusPrivilege()
  {
    const types = this.step1Form.get('type')?.value || [];
    return ['sell', 'purchasing'].some(type => types.includes(type));
  }

  getFinancialStatusPrivilege(){
    return this.step1Form.get('type')?.value.includes('sell');
  }

  getLegalStatusPrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["duplexes", "basements","penthouses","roofs"];
    return checkUnitTypes.includes(unitType) && this.step1Form.get('type')?.value.includes('sell') && !this.getInsideCompoundPrivilege();
  }

  getUnitLayoutStatus()
  {
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["roofs", "basements"];
    return checkUnitTypes.includes(unitType);
  }

  getBuildingLayoutStatus(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["roofs", "standalone_villas" ,"factory_lands" ,"warehouses","commercial_administrative_buildings" ,"residential_buildings"];
    return checkUnitTypes.includes(unitType);
  }
  getGroundLayoutStatus(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["factory_lands","warehouses","residential_buildings","commercial_administrative_buildings"];
    return checkUnitTypes.includes(unitType) && this.step1Form.get('specializationScope')?.value.includes('purchase_sell_outside_compound');
  }

  getUnitDesignPrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["standalone_villas"];
    return checkUnitTypes.includes(unitType);
  }
  getActivityPrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = [
        "factory_lands",
        "warehouses",
        "commercial_administrative_buildings",
        "shops",
        "commercial_stores"
      ];
    return checkUnitTypes.includes(unitType);
  }

  getUnitDescriptionPrivilege(){
    const scope = this.step1Form.get('specializationScope')?.value.includes('purchase_sell_outside_compound');
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["residential_buildings","commercial_administrative_buildings"];
    return checkUnitTypes.includes(unitType) && scope;
  }

  getSubUnitType(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["hotels", "vacation_villa", "chalets"];
    return  checkUnitTypes.includes(unitType);
  }

  getFitOutCondition(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["pharmacies", "factory_lands", "warehouses","commercial_stores","commercial_administrative_buildings"];
    return  checkUnitTypes.includes(unitType);
  }

  getRentRecurrencePrivilege(){
    const usageType = this.step1Form.get('type')?.value;
    return usageType === 'rent_out' || usageType === 'rent_in';
  }

  getFinishingStatus(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["factory_lands","warehouses","residential_buildings" ];
    return checkUnitTypes.includes(unitType);
  }

  getUnitFacing(){
    const unitType = this.step1Form.get('unitType')?.value;
    const usageType = this.step1Form.get('type')?.value;
    const checkUnitTypes = ["apartments", "duplexes", "studios", "penthouses", "chalets", "hotels", "vacation_villa"];

    return (
      checkUnitTypes.includes(unitType) &&
      (usageType === 'rent_out' || usageType === 'sell') &&
      !this.getInsideCompoundPrivilege()
    );
  }

  getImagePrivilege(){
    const type = this.step1Form.get('type')?.value;
    const imageAllowedTypes = ['sell', 'rent_out'];
    const hasImagePrivilege = imageAllowedTypes.includes(type);
    return hasImagePrivilege;
  }

  getPaymentMethodPrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["hotels", "vacation_villa", "chalets"];
    const typeValue = this.step1Form.get('type')?.value;

    return !checkUnitTypes.includes(unitType) && (typeValue === 'sell' || typeValue === 'purchasing');
  }

  getAskingPricePrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["hotels","vacation_villa","chalets" ];
    return !checkUnitTypes.includes(unitType)  && this.step1Form.get('type')?.value.includes('sell') && this.getInsideCompoundPrivilege();
  }

  getAvgUnitPricePrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["hotels","vacation_villa","chalets" ];
    const test2 =  this.step1Form.get('type')?.value.includes('rent_in') && !checkUnitTypes.includes(unitType);
    return this.step1Form.get('type')?.value.includes('purchasing') || test2;
  }

  getAvgUnitPriceMonthlyPrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["hotels","vacation_villa","chalets"];
    const unitUsageType = this.step1Form.get('type')?.value;
    return checkUnitTypes.includes(unitType) &&  (unitUsageType === 'rent_in' || unitUsageType === 'rent_out');
  }

  getUnitPricePrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["hotels","vacation_villa","chalets" ];

    const case1 = !checkUnitTypes.includes(unitType)  && this.step1Form.get('type')?.value.includes('sell') && !this.getInsideCompoundPrivilege();
    const case2 = this.step1Form.get('type')?.value.includes('rent_out') && !checkUnitTypes.includes(unitType);
    const case3 = checkUnitTypes.includes(unitType)  && this.step1Form.get('type')?.value.includes('sell');
    return case1 || case2 || case3;
  }

 getRequestedOverPrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["hotels"];
    return this.step1Form.get('type')?.value.includes('sell') && !checkUnitTypes.includes(unitType);
  }

  getRequiredInsurancePrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["hotels","vacation_villa","chalets" ];
    return this.step1Form.get('type')?.value.includes('rent_out') && !checkUnitTypes.includes(unitType);
  }

  getOtherExpensesPrivilege(){
    const unitType = this.step1Form.get('unitType')?.value;
    const checkUnitTypes = ["hotels","vacation_villa","chalets" ];
    return this.step1Form.get('type')?.value.includes('rent_out') && !checkUnitTypes.includes(unitType);
  }
}
