{"ast": null, "code": "import baseGet from './_baseGet.js';\n\n/**\n * The opposite of `_.property`; this method creates a function that returns\n * the value at a given path of `object`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Util\n * @param {Object} object The object to query.\n * @returns {Function} Returns the new accessor function.\n * @example\n *\n * var array = [0, 1, 2],\n *     object = { 'a': array, 'b': array, 'c': array };\n *\n * _.map(['a[2]', 'c[0]'], _.propertyOf(object));\n * // => [2, 0]\n *\n * _.map([['a', '2'], ['c', '0']], _.propertyOf(object));\n * // => [2, 0]\n */\nfunction propertyOf(object) {\n  return function (path) {\n    return object == null ? undefined : baseGet(object, path);\n  };\n}\nexport default propertyOf;", "map": {"version": 3, "names": ["baseGet", "propertyOf", "object", "path", "undefined"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/propertyOf.js"], "sourcesContent": ["import baseGet from './_baseGet.js';\n\n/**\n * The opposite of `_.property`; this method creates a function that returns\n * the value at a given path of `object`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Util\n * @param {Object} object The object to query.\n * @returns {Function} Returns the new accessor function.\n * @example\n *\n * var array = [0, 1, 2],\n *     object = { 'a': array, 'b': array, 'c': array };\n *\n * _.map(['a[2]', 'c[0]'], _.propertyOf(object));\n * // => [2, 0]\n *\n * _.map([['a', '2'], ['c', '0']], _.propertyOf(object));\n * // => [2, 0]\n */\nfunction propertyOf(object) {\n  return function(path) {\n    return object == null ? undefined : baseGet(object, path);\n  };\n}\n\nexport default propertyOf;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAO,UAASC,IAAI,EAAE;IACpB,OAAOD,MAAM,IAAI,IAAI,GAAGE,SAAS,GAAGJ,OAAO,CAACE,MAAM,EAAEC,IAAI,CAAC;EAC3D,CAAC;AACH;AAEA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}