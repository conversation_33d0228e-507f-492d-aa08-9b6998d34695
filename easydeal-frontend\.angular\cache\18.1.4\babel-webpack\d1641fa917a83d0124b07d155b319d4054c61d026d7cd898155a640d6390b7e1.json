{"ast": null, "code": "import baseSet from './_baseSet.js';\nimport baseZipObject from './_baseZipObject.js';\n\n/**\n * This method is like `_.zipObject` except that it supports property paths.\n *\n * @static\n * @memberOf _\n * @since 4.1.0\n * @category Array\n * @param {Array} [props=[]] The property identifiers.\n * @param {Array} [values=[]] The property values.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.zipObjectDeep(['a.b[0].c', 'a.b[1].d'], [1, 2]);\n * // => { 'a': { 'b': [{ 'c': 1 }, { 'd': 2 }] } }\n */\nfunction zipObjectDeep(props, values) {\n  return baseZipObject(props || [], values || [], baseSet);\n}\nexport default zipObjectDeep;", "map": {"version": 3, "names": ["baseSet", "baseZipObject", "zipObjectDeep", "props", "values"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/zipObjectDeep.js"], "sourcesContent": ["import baseSet from './_baseSet.js';\nimport baseZipObject from './_baseZipObject.js';\n\n/**\n * This method is like `_.zipObject` except that it supports property paths.\n *\n * @static\n * @memberOf _\n * @since 4.1.0\n * @category Array\n * @param {Array} [props=[]] The property identifiers.\n * @param {Array} [values=[]] The property values.\n * @returns {Object} Returns the new object.\n * @example\n *\n * _.zipObjectDeep(['a.b[0].c', 'a.b[1].d'], [1, 2]);\n * // => { 'a': { 'b': [{ 'c': 1 }, { 'd': 2 }] } }\n */\nfunction zipObjectDeep(props, values) {\n  return baseZipObject(props || [], values || [], baseSet);\n}\n\nexport default zipObjectDeep;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,aAAa,MAAM,qBAAqB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACpC,OAAOH,aAAa,CAACE,KAAK,IAAI,EAAE,EAAEC,MAAM,IAAI,EAAE,EAAEJ,OAAO,CAAC;AAC1D;AAEA,eAAeE,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}