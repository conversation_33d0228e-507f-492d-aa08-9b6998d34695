{"ast": null, "code": "/**\n * The base implementation of `_.gt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is greater than `other`,\n *  else `false`.\n */\nfunction baseGt(value, other) {\n  return value > other;\n}\nexport default baseGt;", "map": {"version": 3, "names": ["baseGt", "value", "other"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_baseGt.js"], "sourcesContent": ["/**\n * The base implementation of `_.gt` which doesn't coerce arguments.\n *\n * @private\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is greater than `other`,\n *  else `false`.\n */\nfunction baseGt(value, other) {\n  return value > other;\n}\n\nexport default baseGt;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAC5B,OAAOD,KAAK,GAAGC,KAAK;AACtB;AAEA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}