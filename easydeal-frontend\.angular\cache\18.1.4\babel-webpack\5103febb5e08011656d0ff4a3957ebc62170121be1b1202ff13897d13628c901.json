{"ast": null, "code": "import createPadding from './_createPadding.js';\nimport stringSize from './_stringSize.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/**\n * Pads `string` on the right side if it's shorter than `length`. Padding\n * characters are truncated if they exceed `length`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.padEnd('abc', 6);\n * // => 'abc   '\n *\n * _.padEnd('abc', 6, '_-');\n * // => 'abc_-_'\n *\n * _.padEnd('abc', 3);\n * // => 'abc'\n */\nfunction padEnd(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n  var strLength = length ? stringSize(string) : 0;\n  return length && strLength < length ? string + createPadding(length - strLength, chars) : string;\n}\nexport default padEnd;", "map": {"version": 3, "names": ["createPadding", "stringSize", "toInteger", "toString", "padEnd", "string", "length", "chars", "str<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/padEnd.js"], "sourcesContent": ["import createPadding from './_createPadding.js';\nimport stringSize from './_stringSize.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/**\n * Pads `string` on the right side if it's shorter than `length`. Padding\n * characters are truncated if they exceed `length`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.padEnd('abc', 6);\n * // => 'abc   '\n *\n * _.padEnd('abc', 6, '_-');\n * // => 'abc_-_'\n *\n * _.padEnd('abc', 3);\n * // => 'abc'\n */\nfunction padEnd(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n\n  var strLength = length ? stringSize(string) : 0;\n  return (length && strLength < length)\n    ? (string + createPadding(length - strLength, chars))\n    : string;\n}\n\nexport default padEnd;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EACrCF,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAAC;EACzBC,MAAM,GAAGJ,SAAS,CAACI,MAAM,CAAC;EAE1B,IAAIE,SAAS,GAAGF,MAAM,GAAGL,UAAU,CAACI,MAAM,CAAC,GAAG,CAAC;EAC/C,OAAQC,MAAM,IAAIE,SAAS,GAAGF,MAAM,GAC/BD,MAAM,GAAGL,aAAa,CAACM,MAAM,GAAGE,SAAS,EAAED,KAAK,CAAC,GAClDF,MAAM;AACZ;AAEA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}