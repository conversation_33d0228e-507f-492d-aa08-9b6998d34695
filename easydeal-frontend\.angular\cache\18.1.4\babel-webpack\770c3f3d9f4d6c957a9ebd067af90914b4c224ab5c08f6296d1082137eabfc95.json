{"ast": null, "code": "import castPath from './_castPath.js';\nimport to<PERSON>ey from './_toKey.js';\n\n/**\n * The base implementation of `_.get` without support for default values.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @returns {*} Returns the resolved value.\n */\nfunction baseGet(object, path) {\n  path = castPath(path, object);\n  var index = 0,\n    length = path.length;\n  while (object != null && index < length) {\n    object = object[toKey(path[index++])];\n  }\n  return index && index == length ? object : undefined;\n}\nexport default baseGet;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "to<PERSON><PERSON>", "baseGet", "object", "path", "index", "length", "undefined"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_baseGet.js"], "sourcesContent": ["import castPath from './_castPath.js';\nimport to<PERSON>ey from './_toKey.js';\n\n/**\n * The base implementation of `_.get` without support for default values.\n *\n * @private\n * @param {Object} object The object to query.\n * @param {Array|string} path The path of the property to get.\n * @returns {*} Returns the resolved value.\n */\nfunction baseGet(object, path) {\n  path = castPath(path, object);\n\n  var index = 0,\n      length = path.length;\n\n  while (object != null && index < length) {\n    object = object[toKey(path[index++])];\n  }\n  return (index && index == length) ? object : undefined;\n}\n\nexport default baseGet;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC7BA,IAAI,GAAGJ,QAAQ,CAACI,IAAI,EAAED,MAAM,CAAC;EAE7B,IAAIE,KAAK,GAAG,CAAC;IACTC,MAAM,GAAGF,IAAI,CAACE,MAAM;EAExB,OAAOH,MAAM,IAAI,IAAI,IAAIE,KAAK,GAAGC,MAAM,EAAE;IACvCH,MAAM,GAAGA,MAAM,CAACF,KAAK,CAACG,IAAI,CAACC,KAAK,EAAE,CAAC,CAAC,CAAC;EACvC;EACA,OAAQA,KAAK,IAAIA,KAAK,IAAIC,MAAM,GAAIH,MAAM,GAAGI,SAAS;AACxD;AAEA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}