import { ChangeDetectorRef, Component } from '@angular/core';
import { DeveloperService } from '../services/developer.service';

@Component({
  selector: 'app-developer-dashboard',
  templateUrl: './developer-dashboard.component.html',
  styleUrl: './developer-dashboard.component.scss',
})
export class DeveloperDashboardComponent {

  newRequests: any[] = [];

  unitStats: any = {};

  contractStats: any = {};

   projectStatsData: any = {
    apartments_count: 0,
    buildings_count: 0,
    villas_count: 0,
    duplex_count: 0,
    administrative_units_count: 0,
    commercial_units_count: 0
  };

   numberOfProjects: number = 0;

  private developerId: number = 1;

  constructor(private developerService: DeveloperService ,private cd: ChangeDetectorRef

  ) {}

  ngOnInit() {
    this.loadStatistics();
    this.loadPieChartStatistics();
  }

  loadStatistics() {
    this.developerService.getDeveloperStatistics(this.developerId).subscribe({
        next: (response) => {
          console.log('Statistics response:', response.data);
          this.newRequests =  response.data ;
          this.cd.detectChanges();
        },
        error: (error) => {
          console.error('Error loading statistics:', error);
          this.newRequests = [];
        }
      });
  }

  loadPieChartStatistics() {
    this.developerService.getDeveloperPieChartStatistics(this.developerId).subscribe({
      next: (response) => {
         if (response.data) {
          const data = response.data;
          this.unitStats = data.UnitStats;
          this.contractStats = data.ContractRequestStats;
          this.projectStatsData = data.ProjectStats;
          this.numberOfProjects = data.NumberOfProjects;
          this.cd.detectChanges();
        }
      },
      error: (error) => {
        console.error('Error loading pie chart statistics:', error);
      }
    });
  }



}
