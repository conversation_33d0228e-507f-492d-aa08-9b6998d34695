{"ast": null, "code": "import baseLt from './_baseLt.js';\nimport createRelationalOperation from './_createRelationalOperation.js';\n\n/**\n * Checks if `value` is less than `other`.\n *\n * @static\n * @memberOf _\n * @since 3.9.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is less than `other`,\n *  else `false`.\n * @see _.gt\n * @example\n *\n * _.lt(1, 3);\n * // => true\n *\n * _.lt(3, 3);\n * // => false\n *\n * _.lt(3, 1);\n * // => false\n */\nvar lt = createRelationalOperation(baseLt);\nexport default lt;", "map": {"version": 3, "names": ["baseLt", "createRelationalOperation", "lt"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/lt.js"], "sourcesContent": ["import baseLt from './_baseLt.js';\nimport createRelationalOperation from './_createRelationalOperation.js';\n\n/**\n * Checks if `value` is less than `other`.\n *\n * @static\n * @memberOf _\n * @since 3.9.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is less than `other`,\n *  else `false`.\n * @see _.gt\n * @example\n *\n * _.lt(1, 3);\n * // => true\n *\n * _.lt(3, 3);\n * // => false\n *\n * _.lt(3, 1);\n * // => false\n */\nvar lt = createRelationalOperation(baseLt);\n\nexport default lt;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,yBAAyB,MAAM,iCAAiC;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,EAAE,GAAGD,yBAAyB,CAACD,MAAM,CAAC;AAE1C,eAAeE,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}