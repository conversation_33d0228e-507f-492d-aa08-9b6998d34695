<div class="card card-xl-stretch mb-xl-8">
  <div class="card-header border-0 d-flex justify-content-between">
    <div class="d-flex align-items-center justify-content-start">
      <div class="me-4">
        <div class="d-flex align-items-center position-relative">
          <select
            class="form-select form-select-sm form-select-solid"
            [(ngModel)]="selectedTimePeriod"
            (change)="onTimePeriodChange()"
          >
            <option value="7">Last 7 Days</option>
            <option value="14">Last 14 Days</option>
            <option value="30">Last 30 Days</option>
            <option value="90">Last 90 Days</option>
          </select>
        </div>
      </div>
    </div>
    <div class="card-toolbar d-flex align-items-center">
      <span class="fw-bold ms-2 mx-2">Summary</span>
      <ul class="nav">
        <li class="nav-item">
          <a
            class="nav-link btn btn-sm btn-color-muted btn-active btn-active-light-primary active fw-bold px-3 me-1"
            >{{ chartTitle }}</a
          >
        </li>
      </ul>
    </div>
  </div>
  <div class="card-body">
    <canvas
      baseChart
      [type]="'bar'"
      [data]="{
        labels: barChartLabels,
        datasets: barChartData
      }"
      [options]="barChartOptions"
      style="height: 200px"
    ></canvas>
  </div>
</div>
