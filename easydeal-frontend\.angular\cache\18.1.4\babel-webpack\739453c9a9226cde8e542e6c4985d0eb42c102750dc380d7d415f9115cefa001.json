{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\n\n/**\n * Flattens `array` a single level deep.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flatten([1, [2, [3, [4]], 5]]);\n * // => [1, 2, [3, [4]], 5]\n */\nfunction flatten(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, 1) : [];\n}\nexport default flatten;", "map": {"version": 3, "names": ["baseFlatten", "flatten", "array", "length"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/flatten.js"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\n\n/**\n * Flattens `array` a single level deep.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flatten([1, [2, [3, [4]], 5]]);\n * // => [1, 2, [3, [4]], 5]\n */\nfunction flatten(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, 1) : [];\n}\n\nexport default flatten;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,IAAIC,MAAM,GAAGD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACC,MAAM;EAC7C,OAAOA,MAAM,GAAGH,WAAW,CAACE,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE;AAC5C;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}