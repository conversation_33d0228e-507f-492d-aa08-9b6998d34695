{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\nvar arrayBufferTag = '[object ArrayBuffer]';\n\n/**\n * The base implementation of `_.isArrayBuffer` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array buffer, else `false`.\n */\nfunction baseIsArrayBuffer(value) {\n  return isObjectLike(value) && baseGetTag(value) == arrayBufferTag;\n}\nexport default baseIsArrayBuffer;", "map": {"version": 3, "names": ["baseGetTag", "isObjectLike", "arrayBufferTag", "baseIsArrayBuffer", "value"], "sources": ["C:/Users/<USER>/Desktop/taskes/fronLogin/easydeal-frontend/node_modules/lodash-es/_baseIsArrayBuffer.js"], "sourcesContent": ["import baseGetTag from './_baseGetTag.js';\nimport isObjectLike from './isObjectLike.js';\n\nvar arrayBufferTag = '[object ArrayBuffer]';\n\n/**\n * The base implementation of `_.isArrayBuffer` without Node.js optimizations.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array buffer, else `false`.\n */\nfunction baseIsArrayBuffer(value) {\n  return isObjectLike(value) && baseGetTag(value) == arrayBufferTag;\n}\n\nexport default baseIsArrayBuffer;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,YAAY,MAAM,mBAAmB;AAE5C,IAAIC,cAAc,GAAG,sBAAsB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAChC,OAAOH,YAAY,CAACG,KAAK,CAAC,IAAIJ,UAAU,CAACI,KAAK,CAAC,IAAIF,cAAc;AACnE;AAEA,eAAeC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}