import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Subscription, Observable } from 'rxjs';
import { Router } from '@angular/router';

@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.scss'],
})
export class SignupComponent implements OnInit, OnDestroy {
  signupForm: FormGroup;
  hasError: boolean = false;
  isLoading$: Observable<boolean>;

  // private fields
  private unsubscribe: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private router: Router
  ) {
    // Initialize loading state
    this.isLoading$ = new Observable(observer => {
      observer.next(false);
    });
  }

  ngOnInit(): void {
    this.initForm();
  }

  // convenience getter for easy access to form fields
  get f() {
    return this.signupForm.controls;
  }

  initForm() {
    this.signupForm = this.fb.group({
      userType: ['', Validators.required],
      fullname: [
        '',
        Validators.compose([
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(100),
        ]),
      ],
      email: [
        '',
        Validators.compose([
          Validators.required,
          Validators.email,
          Validators.minLength(3),
          Validators.maxLength(320),
        ]),
      ],
      phone: [
        '',
        Validators.compose([
          Validators.required,
          Validators.minLength(10),
          Validators.maxLength(15),
        ]),
      ],
      password: [
        '',
        Validators.compose([
          Validators.required,
          Validators.minLength(6),
          Validators.maxLength(100),
        ]),
      ],
      confirmPassword: [
        '',
        Validators.compose([
          Validators.required,
          Validators.minLength(6),
          Validators.maxLength(100),
        ]),
      ],
      agree: [false, Validators.compose([Validators.required])],
    });
  }

  submit() {
    this.hasError = false;
    
    if (this.signupForm.invalid) {
      return;
    }

    // Check if passwords match
    if (this.f.password.value !== this.f.confirmPassword.value) {
      this.hasError = true;
      return;
    }

    // Here you would typically call your signup service
    console.log('Signup form submitted:', this.signupForm.value);
    
    // For now, just navigate to login or dashboard
    // this.router.navigate(['/auth/login']);
  }

  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}
