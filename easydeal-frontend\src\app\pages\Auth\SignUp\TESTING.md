# تعليمات اختبار صفحة التسجيل

## كيفية تشغيل التطبيق

### 1. تشغيل الخادم المحلي
```bash
cd easydeal-frontend
ng serve
```

### 2. الوصول للصفحة
افتح المتصفح واذهب إلى: `http://localhost:4200/Signup`

## اختبار الوظائف

### 1. اختبار التصميم
- ✅ تحقق من ظهور صورة الخلفية بشكل صحيح
- ✅ تحقق من ظهور اللوجو في الجانب الأيسر
- ✅ تحقق من ظهور العنوان "مرحباً بك في إيزي ديل"
- ✅ تحقق من ظهور الفورم في الوسط مع خلفية شفافة
- ✅ تحقق من التصميم المتجاوب على الهواتف المحمولة

### 2. اختبار الحقول
- ✅ **نوع المستخدم**: تحقق من وجود الخيارات الثلاثة
- ✅ **الاسم الكامل**: اختبر validation للحد الأدنى 3 أحرف
- ✅ **البريد الإلكتروني**: اختبر validation للإيميل الصحيح
- ✅ **رقم الهاتف**: اختبر validation للأرقام
- ✅ **كلمة المرور**: اختبر validation للحد الأدنى 6 أحرف
- ✅ **تأكيد كلمة المرور**: اختبر تطابق كلمات المرور
- ✅ **الموافقة على الشروط**: اختبر أنه مطلوب

### 3. اختبار رسائل الخطأ
- ✅ تحقق من ظهور رسائل الخطأ باللغة العربية
- ✅ تحقق من ظهور رسائل الخطأ عند ترك الحقول فارغة
- ✅ تحقق من ظهور رسالة خطأ عند عدم تطابق كلمات المرور

### 4. اختبار الإرسال
- ✅ تحقق من تعطيل زر التسجيل عند وجود أخطاء
- ✅ تحقق من تفعيل زر التسجيل عند ملء جميع الحقول بشكل صحيح
- ✅ تحقق من عمل وظيفة الإرسال (console.log)

## اختبار الوحدات (Unit Tests)

### تشغيل الاختبارات
```bash
ng test --include='**/signup.component.spec.ts'
```

### الاختبارات المتوفرة
1. **إنشاء المكون**: تحقق من إنشاء المكون بنجاح
2. **تهيئة الفورم**: تحقق من القيم الافتراضية
3. **validation الحقول المطلوبة**: تحقق من جميع الحقول المطلوبة
4. **validation الإيميل**: تحقق من صحة تنسيق الإيميل
5. **validation الحد الأدنى للأحرف**: تحقق من الاسم وكلمة المرور
6. **تطابق كلمات المرور**: تحقق من رسالة الخطأ
7. **منع الإرسال عند وجود أخطاء**: تحقق من عدم الإرسال
8. **السماح بالإرسال عند صحة البيانات**: تحقق من الإرسال

## اختبار التوافق

### المتصفحات
- ✅ Chrome
- ✅ Firefox
- ✅ Safari
- ✅ Edge

### الأجهزة
- ✅ Desktop (1920x1080)
- ✅ Tablet (768x1024)
- ✅ Mobile (375x667)

## مشاكل محتملة وحلولها

### 1. صورة الخلفية لا تظهر
**الحل**: تحقق من وجود الملف في `assets/media/login/EaseDealPage.png`

### 2. اللوجو لا يظهر
**الحل**: تحقق من وجود الملف في `assets/media/easydeallogos/Easy Deal.png`

### 3. الأنماط لا تطبق بشكل صحيح
**الحل**: تحقق من تضمين Bootstrap في التطبيق

### 4. خطأ في التوجيه
**الحل**: تحقق من إضافة المسار في `pages/routing.ts`

## ملاحظات إضافية
- الصفحة تستخدم Angular Reactive Forms
- جميع النصوص باللغة العربية
- التصميم متوافق مع Bootstrap
- الصفحة تدعم RTL (Right-to-Left)
