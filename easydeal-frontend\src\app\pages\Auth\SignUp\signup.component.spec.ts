import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';

import { SignupComponent } from './signup.component';

describe('SignupComponent', () => {
  let component: SignupComponent;
  let fixture: ComponentFixture<SignupComponent>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      declarations: [SignupComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: Router, useValue: routerSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SignupComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.signupForm.get('userType')?.value).toBe('');
    expect(component.signupForm.get('fullname')?.value).toBe('');
    expect(component.signupForm.get('email')?.value).toBe('');
    expect(component.signupForm.get('phone')?.value).toBe('');
    expect(component.signupForm.get('password')?.value).toBe('');
    expect(component.signupForm.get('confirmPassword')?.value).toBe('');
    expect(component.signupForm.get('agree')?.value).toBe(false);
  });

  it('should validate required fields', () => {
    const form = component.signupForm;
    
    expect(form.valid).toBeFalsy();
    
    // Test userType validation
    expect(form.get('userType')?.hasError('required')).toBeTruthy();
    
    // Test fullname validation
    expect(form.get('fullname')?.hasError('required')).toBeTruthy();
    
    // Test email validation
    expect(form.get('email')?.hasError('required')).toBeTruthy();
    
    // Test phone validation
    expect(form.get('phone')?.hasError('required')).toBeTruthy();
    
    // Test password validation
    expect(form.get('password')?.hasError('required')).toBeTruthy();
    
    // Test confirmPassword validation
    expect(form.get('confirmPassword')?.hasError('required')).toBeTruthy();
    
    // Test agree validation
    expect(form.get('agree')?.hasError('required')).toBeTruthy();
  });

  it('should validate email format', () => {
    const emailControl = component.signupForm.get('email');
    
    emailControl?.setValue('invalid-email');
    expect(emailControl?.hasError('email')).toBeTruthy();
    
    emailControl?.setValue('<EMAIL>');
    expect(emailControl?.hasError('email')).toBeFalsy();
  });

  it('should validate minimum length for fullname', () => {
    const fullnameControl = component.signupForm.get('fullname');
    
    fullnameControl?.setValue('ab');
    expect(fullnameControl?.hasError('minlength')).toBeTruthy();
    
    fullnameControl?.setValue('abc');
    expect(fullnameControl?.hasError('minlength')).toBeFalsy();
  });

  it('should validate minimum length for password', () => {
    const passwordControl = component.signupForm.get('password');
    
    passwordControl?.setValue('12345');
    expect(passwordControl?.hasError('minlength')).toBeTruthy();
    
    passwordControl?.setValue('123456');
    expect(passwordControl?.hasError('minlength')).toBeFalsy();
  });

  it('should set hasError to true when passwords do not match', () => {
    component.signupForm.patchValue({
      userType: 'client',
      fullname: 'Test User',
      email: '<EMAIL>',
      phone: '1234567890',
      password: 'password123',
      confirmPassword: 'different123',
      agree: true
    });

    component.submit();
    
    expect(component.hasError).toBeTruthy();
  });

  it('should not submit when form is invalid', () => {
    component.signupForm.patchValue({
      userType: '',
      fullname: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      agree: false
    });

    const consoleSpy = spyOn(console, 'log');
    component.submit();
    
    expect(consoleSpy).not.toHaveBeenCalled();
  });

  it('should submit when form is valid and passwords match', () => {
    component.signupForm.patchValue({
      userType: 'client',
      fullname: 'Test User',
      email: '<EMAIL>',
      phone: '1234567890',
      password: 'password123',
      confirmPassword: 'password123',
      agree: true
    });

    const consoleSpy = spyOn(console, 'log');
    component.submit();
    
    expect(component.hasError).toBeFalsy();
    expect(consoleSpy).toHaveBeenCalledWith('Signup form submitted:', component.signupForm.value);
  });
});
