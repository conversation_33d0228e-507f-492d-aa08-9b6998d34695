<div class="card card-flush bg-{{ backgroundColor }}">
  <div class="card-header border-0 d-flex justify-content-start">
    <span class="badge badge-light-{{ backgroundColor }} fw-bold me-2 fs-base">
      {{ title }}
    </span>
  </div>

  <div class="card-body p-0">
    <div class="card-p bg-{{ backgroundColor }}">
      <div class="d-flex justify-content-between align-items-center">
        <div class="stat-inline text-white">
          <span class="stat-label">Count:</span>
          <span class="stat-value">{{ activeRequests }}</span>
        </div>

        <div class="stat-inline text-white">
          <span class="stat-label">NumberOfProjects:</span>
          <span class="stat-value">{{ totalRequests }}</span>
        </div>
      </div>
    </div>
  </div>
</div>